import { ReactNode } from 'react';

interface DataListFooterProps {
  children?: ReactNode;
  extra?: string | ReactNode;
}

export const DataListFooter = ({ children, extra }: DataListFooterProps) => {
  return (
    <div className="text-base space-x-2 flex">
      <div className="flex-1 gap-2 flex">{children}</div>
      {extra && <div className="flex-shrink-0 flex-grow-0 space-x-2">{extra}</div>}
    </div>
  );
};

DataListFooter.displayName = 'DataListFooterProps';
