import { getOrgs } from '@/services/dataResources/orgsApi';
import {
  AddAccountItem,
  DeleteAccountItem,
  EditAccountItem,
  GetAccountList,
} from '@/services/securityManage';
import { useModel } from '@umijs/max';
import { Button, Form, Input, message, Modal, Popconfirm, Select, Space, Table } from 'antd';
import { useEffect, useState } from 'react';

import { AccountItem, AccountResponse } from './type';

const ROLES_OPTIONS = {
  sa: [
    { label: '公司 / 部门负责人', value: 'dept_head' },
    { label: '公司 / 部门管理员', value: 'dept_admin' },
    { label: '业务员', value: 'user' },
    { label: '普通用户', value: 'reporter' },
  ],
  dept_head: [
    { label: '公司 / 部门管理员', value: 'dept_admin' },
    { label: '业务员', value: 'user' },
    { label: '普通用户', value: 'reporter' },
  ],
  dept_admin: [
    { label: '业务员', value: 'user' },
    { label: '普通用户', value: 'reporter' },
  ],
};

export default function Account() {
  const { initialState } = useModel('@@initialState');
  const currentUserRole = initialState?.currentUser?.roles[0] ?? 'user';
  const currentUserId = initialState?.currentUser?.id;

  const [isAccountManagerOpen, setIsAccountManagerOpen] = useState(false);

  const [accountList, setAccountList] = useState<AccountItem[]>([]);
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const [orgsData, setOrgsData] = useState<any[]>([]);

  const getAccountList = async () => {
    setLoading(true);
    try {
      const res: AccountResponse = await GetAccountList();

      if (res.code === 200001) {
        setAccountList(res.data.list);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  const editAccountItem = async (values: AccountItem) => {
    setLoading(true);
    try {
      const res = await EditAccountItem(values);
      if (res.code === 200001) {
        message.success('编辑成功');
        setIsAccountManagerOpen(false);
        getAccountList();
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
      form.resetFields();
    }
  };

  const deleteAccountItem = async (id: string) => {
    setLoading(true);
    try {
      const res = await DeleteAccountItem([id]);
      if (res.code === 200001) {
        message.success('删除成功');
        getAccountList();
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  const addAccountItem = async (values: Partial<AccountItem>) => {
    setLoading(true);
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { _password, ...params } = values;
      const res = await AddAccountItem({ ...params, status: true });
      if (res.code === 200001) {
        message.success('添加成功');
        setIsAccountManagerOpen(false);
        getAccountList();
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
      form.resetFields();
    }
  };

  const getOrgsData = async () => {
    try {
      const res: any = await getOrgs();
      if (res?.code === 200001) {
        setOrgsData(
          res.data.filter(
            (item: any) =>
              item.parent_id === '' || item.parent_id === 'bb8d6171-bdc6-468d-b954-098d794fb6bd',
          ),
        );
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    }
  };

  useEffect(() => {
    getAccountList();
    getOrgsData();
  }, []);

  const columns = [
    { title: '姓名', dataIndex: 'full_name' },
    { title: '手机号', dataIndex: 'mobile' },
    { title: '邮箱', dataIndex: 'email' },
    { title: '角色', dataIndex: 'roles' },
    {
      title: '操作',
      render: (record: AccountItem) => {
        let isEditable = true;
        let isDeletable = true;
        const accountRole = record.roles[0];

        if (accountRole === 'dept_head') {
          isDeletable = false;
          isEditable = currentUserRole === 'sa';
        }
        if (accountRole === 'sa') {
          isDeletable = false;
          isEditable = currentUserId === record.id;
        }

        return (
          <Space size="small">
            {isEditable && (
              <Button
                type="link"
                onClick={() => {
                  form.setFieldsValue(record);
                  setIsAccountManagerOpen(true);
                }}
              >
                编辑
              </Button>
            )}

            {isDeletable && (
              <Popconfirm
                cancelText="取消"
                okText="确定"
                onConfirm={() => deleteAccountItem(record.id)}
                title="确定删除吗？"
              >
                <Button type="link" danger disabled={!isDeletable}>
                  删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <div className="p-4">
      <Modal
        maskClosable={false}
        cancelText="取消"
        confirmLoading={loading}
        okText="确定"
        onCancel={() => {
          setIsAccountManagerOpen(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        open={isAccountManagerOpen}
        title={form.getFieldValue('id') ? '编辑账号' : '添加账号'}
        footer={null}
      >
        <Form
          className="p-4"
          form={form}
          labelCol={{ span: 8 }}
          onFinish={(values) => {
            if (form.getFieldValue('id')) {
              editAccountItem({ id: form.getFieldValue('id'), ...values });
            } else {
              addAccountItem(values);
            }
          }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          {!form.getFieldValue('id') && (
            <>
              <Form.Item
                label="密码"
                name="password"
                rules={[{ message: '请输入密码', required: true }]}
              >
                <Input placeholder="请输入密码" type="password" />
              </Form.Item>

              <Form.Item
                label="确认密码"
                name="_password"
                rules={[
                  { required: true, message: '请再次输入密码' },
                  () => ({
                    validator(_, value) {
                      if (!value || form.getFieldValue('password') === value)
                        return Promise.resolve();
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
                validateTrigger="onBlur"
              >
                <Input placeholder="请再次输入密码" type="password" />
              </Form.Item>
            </>
          )}

          <Form.Item
            label="姓名"
            name="full_name"
            rules={[{ message: '请输入姓名', required: true }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            label="所属部门/公司"
            name="company_id"
            rules={[{ message: '请选择所属部门/公司', required: true }]}
          >
            <Select
              placeholder="请选择所属部门/公司"
              options={orgsData.map((org) => ({ label: org.name, value: org.id }))}
            />
          </Form.Item>

          <Form.Item
            label="角色"
            name="roles"
            rules={[
              { required: true, message: '请选择角色' },
              { max: 1, type: 'array', message: '最多选择一个角色' },
            ]}
          >
            <Select
              allowClear
              mode="tags"
              onChange={(value) => {
                if (value.length > 1) {
                  form.setFieldsValue({ roles: [value[value.length - 1]] });
                }
              }}
              options={ROLES_OPTIONS[currentUserRole]}
              placeholder="请选择角色"
            />
          </Form.Item>

          <Form.Item
            label="手机号"
            name="mobile"
            rules={[{ message: '请输入手机号', required: true }]}
          >
            <Input placeholder="请输入手机号" />
          </Form.Item>

          <Form.Item label="邮箱" name="email" rules={[{ message: '请输入邮箱', required: true }]}>
            <Input placeholder="请输入邮箱" />
          </Form.Item>
        </Form>

        <Form.Item style={{ textAlign: 'right' }}>
          <Button
            onClick={() => {
              setIsAccountManagerOpen(false);
              form.resetFields();
            }}
            style={{ marginRight: 16 }}
          >
            取消
          </Button>
          {form.getFieldValue('id') ? (
            <Popconfirm
              cancelText="取消"
              okText="确定"
              onConfirm={() => form.submit()}
              title="确认修改?"
            >
              <Button loading={loading} type="primary">
                确定
              </Button>
            </Popconfirm>
          ) : (
            <Button loading={loading} onClick={() => form.submit()} type="primary">
              确定
            </Button>
          )}
        </Form.Item>
      </Modal>

      <div className="flex items-center justify-between">
        账号管理
        <Button onClick={() => setIsAccountManagerOpen(true)} type="primary">
          添加账号
        </Button>
      </div>
      <div className="mt-4">
        <Table
          columns={columns}
          dataSource={accountList}
          loading={loading}
          pagination={false}
          rowKey="id"
          size="small"
        />
      </div>
    </div>
  );
}
