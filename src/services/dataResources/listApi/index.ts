import { getToken } from '@/utils/loginUntils';
import { request } from 'umi';
//资源列表查询
export async function GetListData(data: any) {
  return request(`/api/v1/resources`, {
    method: 'GET',
    params: data,
    headers: {
      authorization: getToken() || '',
    },
  });
}

//资源统计查询
export async function GetStatisticsData(data: any) {
  return request(`/api/v1/resources/statistics`, {
    method: 'GET',
    params: data,
    headers: {
      authorization: getToken() || '',
    },
  });
}

//资源详情
export async function GetListDetail(id: string) {
  return request(`/api/v1/resources/${id}`, {
    method: 'GET',
    headers: {
      authorization: getToken() || '',
    },
  });
}

//资源详情申请试用弹窗信息
export async function GetDetailModalInfo(id: string) {
  return request(`/api/v1/resources/${id}/use-apl`, {
    method: 'GET',
    headers: {
      authorization: getToken() || '',
    },
  });
}

//资源详情申请使用
export async function PostDetailModalInfo(id: string, data: any) {
  return request(`/api/v1/resources/${id}/use-apl`, {
    method: 'POST',
    data,
    headers: {
      authorization: getToken() || '',
    },
  });
}

// 主数据申请使用
export async function PostMajorDataApply(id: string, data: any) {
  return request(`/api/v1/major-data-use/${id}/use-apl`, {
    method: 'POST',
    data,
    headers: {
      authorization: getToken() || '',
    },
  });
}

// 主数据详情列表
export async function GetMajorDataDetail(id: string, params: any) {
  return request(`/api/v1/major-data/${id}/api-major-data`, {
    method: 'GET',
    params,
    headers: {
      authorization: getToken() || '',
    },
  });
}

// 主数据 id
export async function GetMajorDataList() {
  return request(`/api/v1/major-data`, {
    method: 'GET',
    headers: {
      authorization: getToken() || '',
    },
  });
}
