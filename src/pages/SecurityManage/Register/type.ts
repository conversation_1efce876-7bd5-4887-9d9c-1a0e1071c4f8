export interface RegisterItem {
  avatar: string;
  company_id: string;
  created_at: string;
  deleted_at: null;
  dept_id: null | string;
  email: string;
  full_name: string;
  id: string;
  mobile: string;
  position: string;
  register_status: string;
  roles: string[];
  status: boolean;
  updated_at: string;
  username: string;
}

export interface RegisterResponse {
  code: number;
  data: {
    list: RegisterItem[];
    total: number;
  };
  message: string;
}
