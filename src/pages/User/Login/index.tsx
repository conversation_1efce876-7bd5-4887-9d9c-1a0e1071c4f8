import { LoginPost } from '@/services/Login';
import { saveToken } from '@/utils/loginUntils';
import { Helmet, history, useIntl, useModel } from '@umijs/max';
import { Button, Form, Input, message } from 'antd';
import React, { useState } from 'react';
import { flushSync } from 'react-dom';
import Settings from '../../../../config/defaultSettings';

const Login: React.FC = () => {
  const { setInitialState } = useModel('@@initialState');
  const intl = useIntl();

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  return (
    <>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.login',
            defaultMessage: '登录页',
          })}
          - {Settings.title}
        </title>
      </Helmet>

      <div className="bg-[#002FA50D] h-screen w-screen flex">
        <div className="w-[559px] h-[387px] m-auto bg-white p-10 flex flex-col justify-between">
          <div className="text-[25px] font-bold text-center leading-[44px]">欢迎登录</div>

          <Form
            form={form}
            onFinish={async (values: any) => {
              setLoading(true);
              const res = await LoginPost(values);
              if (res.code === 200001) {
                saveToken(res.data.token);
                if (res.data.user_info) {
                  flushSync(() => {
                    setInitialState((s) => ({
                      ...s,
                      currentUser: res.data.user_info,
                    }));
                  });
                }
                message.success('登录成功');
                const urlParams = new URL(window.location.href).searchParams;
                history.push(urlParams.get('redirect') || '/');
                setLoading(false);
                return;
              } else {
                message.error(res.message);
                setLoading(false);
              }
            }}
          >
            <Form.Item name="username" rules={[{ required: true, message: '请输入用户名!' }]}>
              <Input placeholder="请输入用户名" className="h-12 rounded-none" />
            </Form.Item>
            <Form.Item name="password" rules={[{ required: true, message: '请输入密码!' }]}>
              <Input.Password placeholder="请输入密码" className="h-12 rounded-none" />
            </Form.Item>
          </Form>

          <div className="flex justify-between">
            <Button
              type="text"
              className=" text-[#616161]"
              onClick={() => history.push('/user/register')}
            >
              注册账号
            </Button>
            <Button
              type="text"
              className=" text-[#616161]"
              onClick={() => history.push('/user/modify')}
            >
              忘记密码?
            </Button>
          </div>

          <Button
            type="primary"
            className="w-full h-12 rounded-none"
            onClick={() => form.submit()}
            loading={loading}
          >
            登录
          </Button>
        </div>
      </div>
    </>
  );
};

export default Login;
