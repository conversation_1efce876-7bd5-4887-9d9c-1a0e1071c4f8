import { Avatar<PERSON>ropdown, AvatarName, Footer } from '@/components';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import { message } from 'antd';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import { GetUserInfo } from './services/Login';
import { clearToken, getToken, login_uri } from './utils/loginUntils';
const loginPath = '/user/uc-login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState() {
  const { location } = history;

  // 封装获取用户信息的方法，返回用户数据或 undefined
  const fetchUserInfo = async () => {
    try {
      const res = await GetUserInfo();
      if (res?.code === 200001 && res.data) {
        return res.data;
      }
      // 非正常返回（例如 token 失效），清理并跳转登录
      message.error(res?.message || '获取用户信息失败');
      clearToken();
      history.push(login_uri);
    } catch (error) {
      message.error(JSON.stringify(error));
      clearToken();
      history.push(login_uri);
    }
    return undefined;
  };

  // 如果是回调页，不再尝试获取用户信息
  if (location.pathname === loginPath) {
    return;
  }

  const token = getToken();
  if (!token) {
    // 没有 token，跳转到内部登录页面（用于拿 code 再换 token）
    history.push(loginPath);
    return {
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  // 有 token，尝试拉取用户信息
  const currentUser = await fetchUserInfo();
  return {
    currentUser,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    actionsRender: () => [],
    avatarProps: {
      src: initialState?.currentUser?.avatar,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    logo: '/icons/logo.svg',
    waterMarkProps: {
      content: initialState?.currentUser?.name,
    },
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        history.push(login_uri);
      }
    },
    bgLayoutImgList: [],
    links: [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return <>{children}</>;
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  ...errorConfig,
  requestInterceptors: [
    (url, options) => {
      options.headers = {
        ...options.headers,
        authorization: `Bearer ${localStorage.getItem('poly-bigData-token')}`,
      };
      return {
        url,
        options,
      };
    },
  ],
};
