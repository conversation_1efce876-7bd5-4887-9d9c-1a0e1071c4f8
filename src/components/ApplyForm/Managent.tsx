import { Col, Form, Input, Row } from 'antd';
import { type Rule } from 'antd/es/form';
import { useContext } from 'react';
import { Context } from '.';

interface ApplyFormManagentProps {
  rules?: Record<string, Rule[]>;
}

// 管理信息
export const ApplyFormManagent = ({ rules }: ApplyFormManagentProps) => {
  const { itemSpan, itemGutter } = useContext(Context);
  return (
    <>
      <Row gutter={itemGutter}>
        <Col span={itemSpan}>
          <Form.Item name="head_name" label="数据资源负责人姓名" rules={rules?.head_name}>
            <Input placeholder="请输入负责人姓名" />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="head_phone" label="数据资源负责人联系电话" rules={rules?.head_phone}>
            <Input placeholder="请输入负责人联系电话" />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="head_email" label="数据资源负责人电子邮箱" rules={rules?.head_email}>
            <Input placeholder="请输入负责人电子邮箱" />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="remark" label="备注" rules={rules?.remark}>
            <Input placeholder="请输入备注" />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

ApplyFormManagent.displayName = 'ApplyFormManagent';
