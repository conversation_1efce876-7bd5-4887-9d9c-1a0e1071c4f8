import { Button, Col, Form, Input, Radio, Row, Select } from 'antd';

export default function ListEdit() {
  return (
    <div className="">
      <div className="right-[24px] absolute top-[24px]">
        <Button type="primary">提交申请</Button>
      </div>
      <div className="flex items-center justify-center font-semibold text-[14px] mt-[40px]">
        <span>资源目录信息上报申请</span>
      </div>
      <Form>
        <div className="edit_application_form_sqrxx">
          <div className="flex text-[16px] font-normal leading-[22px] my-5">
            <div className="h-[22px] w-[5px] bg-[#002FA5] mr-[10px]"></div>
            基础信息
          </div>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="数据资源序列编号" name="company">
                <Input disabled placeholder="系统自动生成" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="数据资源中文名称"
                name="name"
                rules={[{ required: true, message: '数据资源中文名称为必填' }]}
              >
                <Input placeholder="请输入中文名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="拥有公司及部门"
                name="company"
                rules={[{ required: true, message: '拥有公司及部门为必填' }]}
              >
                <Select placeholder="请选择拥有者及部门" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="来源渠道类别"
                name="position"
                rules={[{ required: true, message: '来源渠道类别为必填' }]}
              >
                <Select placeholder="请选择来源渠道类别" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="来源系统或报表名称"
                name="formName"
                rules={[{ required: true, message: '联系方式为必填' }]}
              >
                <Select placeholder="请选择来源系统或报表名称" />
              </Form.Item>
            </Col>
          </Row>
        </div>
        <div className="edit_application_form_sqrxx">
          <div className="flex text-[16px] font-normal leading-[22px] my-5">
            <div className="h-[22px] w-[5px] bg-[#002FA5] mr-[10px]"></div>
            业务信息
          </div>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="一级业务域"
                name="yjywy"
                rules={[{ required: true, message: '一级业务域为必填' }]}
              >
                <Select placeholder="请选择一级业务域" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="二级业务域"
                name="ejywy"
                rules={[{ required: true, message: '二级业务域为必填' }]}
              >
                <Select placeholder="请选择二级业务域" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="三级业务域"
                name="sjywy"
                rules={[{ required: true, message: '三级业务域为必填' }]}
              >
                <Select placeholder="请选择三级业务域" />
              </Form.Item>
            </Col>
          </Row>
        </div>
        <div className="edit_application_form_sqrxx">
          <div className="flex text-[16px] font-normal leading-[22px] my-5">
            <div className="h-[22px] w-[5px] bg-[#002FA5] mr-[10px]"></div>
            技术信息
          </div>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="主要数据信息"
                name="declare"
                rules={[{ required: true, message: '主要数据信息为必填' }]}
              >
                <Radio.Group>
                  <Radio value={1}>线上信息系统</Radio>
                  <Radio value={2}>线下手工报表</Radio>
                  <Radio value={3}>其他数据来源</Radio>
                </Radio.Group>
                <Form.Item
                  name="declare"
                  rules={[{ required: true, message: '主要数据信息为必填' }]}
                  style={{ marginTop: '24px' }}
                >
                  <Input.TextArea
                    style={{
                      height: '80px',
                    }}
                    showCount
                    maxLength={100}
                  />
                </Form.Item>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="数据安全等级"
                name="aqsjdj"
                rules={[{ required: true, message: '数据安全等级为必填' }]}
              >
                <Select placeholder="请选择数据安全等级" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="开放共享类型"
                name="kfgxlx"
                rules={[{ required: true, message: '开放共享类型为必填' }]}
              >
                <Select placeholder="请选择开放共享类型" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="敏感信息强调说明"
                name="mgxxqdsm"
                rules={[{ required: true, message: '敏感信息强调说明为必填' }]}
              >
                <Input placeholder="请输入敏感信息强调说明" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="敏感信息采集或共享策略"
                name="mgxxgxcl"
                rules={[{ required: true, message: '敏感信息采集或共享策略为必填' }]}
              >
                <Select placeholder="请选择敏感信息采集或共享策略" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="更新频率"
                name="gxpl"
                rules={[{ required: true, message: '更新频率为必填' }]}
              >
                <Select placeholder="请选择更新频率" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="业务规模"
                name="ywgm"
                rules={[{ required: true, message: '业务规模为必填' }]}
              >
                <Select placeholder="请选择业务规模" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="数据信息可用性说明"
                name="sjxxkyxsm"
                rules={[{ required: true, message: '数据信息可用性说明为必填' }]}
              >
                <Select placeholder="请选择数据信息可用性说明" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="数据库或文件类型"
                name="sjkhwjlx"
                rules={[{ required: true, message: '数据库或文件类型为必填' }]}
              >
                <Select placeholder="请选择数据库或文件类型" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="所在库或服务器名称"
                name="szkhfwqmc"
                rules={[{ required: true, message: '所在库或服务器名称为必填' }]}
              >
                <Select placeholder="请选择所在库或服务器名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="所在表或文件路径名称"
                name="szbhwjljmc"
                rules={[{ required: true, message: '所在表或文件路径名称为必填' }]}
              >
                <Select placeholder="请选择所在表或文件路径名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="数据资源存储形式"
                name="sjzyccfs"
                rules={[{ required: true, message: '数据资源存储形式为必填' }]}
              >
                <Select placeholder="请选择数据资源存储形式" />
              </Form.Item>
            </Col>
          </Row>
        </div>
        <div className="edit_application_form_sqrxx">
          <div className="flex text-[16px] font-normal leading-[22px] my-5">
            <div className="h-[22px] w-[5px] bg-[#002FA5] mr-[10px]"></div>
            管理信息
          </div>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="数据资源负责人姓名"
                name="zyfzrxm"
                rules={[{ required: true, message: '数据资源负责人姓名为必填' }]}
              >
                <Select placeholder="请选择数据资源负责人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="数据资源负责人联系电话"
                name="sjzyfzrlxdh"
                rules={[{ required: true, message: '数据资源负责人联系电话为必填' }]}
              >
                <Input placeholder="请选择数据资源负责人联系电话" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="数据资源负责人电子邮箱"
                name="zyfzrdzyx"
                rules={[{ required: true, message: '数据资源负责人电子邮箱为必填' }]}
              >
                <Input placeholder="请选择数据资源负责人电子邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="备注" name="bz">
                <Input placeholder="请选择备注" />
              </Form.Item>
            </Col>
          </Row>
        </div>
      </Form>
    </div>
  );
}
