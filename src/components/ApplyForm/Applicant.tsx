import { Col, Form, Input, Row } from 'antd';
import { type Rule } from 'antd/es/form';
import { useContext } from 'react';
import { Context } from '.';

// 申请人信息
export const ApplyFormApplicant = ({
  disabled,
  rules,
}: {
  disabled?: boolean;
  rules?: Record<string, Rule[]>;
}) => {
  const { itemSpan, itemGutter } = useContext(Context);
  return (
    <>
      <Row gutter={itemGutter}>
        <Col span={itemSpan}>
          <Form.Item label="单位名称" name="applicant_company" rules={rules?.applicant_company}>
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item label="所属公司" name="applicant_dept" rules={rules?.applicant_dept}>
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item label="姓名" name="applicant_name" rules={rules?.applicant_name}>
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item label="角色" name="applicant_roles_str" rules={rules?.applicant_roles_str}>
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item label="联系方式" name="applicant_mobile" rules={rules?.applicant_mobile}>
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item label="申请日期" name="applicant_date" rules={rules?.applicant_date}>
            <Input disabled={disabled} />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

ApplyFormApplicant.displayName = 'ApplyFormApplicant';
