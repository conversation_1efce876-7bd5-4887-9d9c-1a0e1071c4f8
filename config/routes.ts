﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
      {
        name: 'uc',
        path: '/user/uc-login',
        component: './User/UC',
      },
      {
        name: 'register',
        path: '/user/register',
        component: './User/Register',
      },
      {
        name: 'modify',
        path: '/user/modify',
        component: './User/Modify',
      },
    ],
  },
  {
    path: '/dataResources',
    name: '数据资源',
    //icon: 'smile',
    routes: [
      //资源目录
      {
        path: '/dataResources/list',
        name: '资源目录',
        component: './DataResources/List',
      },
      {
        path: '/dataResources/dataAggregation',
        name: '数据汇聚',
        hideInMenu: true,
        component: './DataAggregation',
      },
      {
        path: '/dataResources/dataAggregation/collectionStatus',
        name: '采集情况',
        hideInMenu: true,
        component: './DataAggregation/CollectionStatus',
      },
      {
        path: '/dataResources/dataAggregation/collectionInfo',
        name: '采集详情',
        hideInMenu: true,
        component: './DataAggregation/CollectionInfo',
      },
      {
        path: '/dataResources/dashboard',
        name: '主数据应用',
        component: './DataResources/Dashboard',
      },
      {
        path: '/dataResources/governanceDashboard',
        name: '主数据治理看板',
        hideInMenu: true,
        component: './DataResources/GovernanceDashboard',
      },
      //资源目录详情
      {
        path: '/dataResources/list/detail/:id',
        name: '资源目录详情',
        hideInMenu: true,
        component: './DataResources/List/Detail',
      },
      //资源目录编辑
      {
        path: '/dataResources/list/edit/:id',
        name: '资源目录编辑',
        hideInMenu: true,
        component: './DataResources/List/Edit',
      },
      //上报申请
      {
        path: '/dataResources/report',
        name: '上报申请管理',
        component: './DataResources/Report',
      },
      {
        path: '/dataResources/report/apply',
        name: '资源目录信息上报申请',
        component: './DataResources/Report/Apply',
        hideInMenu: true,
      },
      //使用申请
      {
        path: '/dataResources/apply',
        name: '使用申请管理',
        access: 'normal',
        component: './DataResources/Apply',
      },
      //资源更新动态
      {
        path: '/dataResources/resourceUpdates',
        name: '资源更新动态',
        access: 'normal',
        component: './DataResources/ResourceUpdates',
      },
    ],
  },
  {
    path: '/dataCollection',
    name: '数据采集',
    access: 'canAdmin',
    routes: [
      // 数据采集加载
      {
        path: '/dataCollection/loading',
        name: '数据采集加载',
        component: './DataCollection/DataLoading',
      },
      // 采集任务
      {
        path: '/dataCollection/task',
        name: '采集任务',
        component: './DataCollection/Task',
      },
      // 元数据
      {
        path: '/dataCollection/metadata',
        name: '元数据',
        component: './DataCollection/Metadata',
      },
    ],
  },
  {
    path: '/dataGovernance',
    name: '数据治理',
    access: 'canAdmin',
    routes: [
      // 数据标准
      {
        path: '/dataGovernance/dataStandards',
        name: '数据标准',
        component: './DataGovernance/DataStandards',
      },
      // 数据治理
      {
        path: '/dataGovernance/dataQuality',
        name: '数据治理',
        component: './DataGovernance/DataQuality',
      },
    ],
  },
  {
    path: '/dataApplication',
    name: '数据应用',
    access: 'canAdmin',
    routes: [
      // 数据服务
      {
        path: '/dataApplication/dataServices',
        name: '数据服务',
        component: './DataApplication/DataServices',
      },
    ],
  },
  {
    path: '/securityManage',
    name: '安全管理',
    access: 'accessToAccountManagement',
    routes: [
      // 账号管理
      {
        path: '/securityManage/account',
        name: '账号管理',
        component: './SecurityManage/Account',
      },
      // 注册申请管理
      {
        path: '/securityManage/register',
        name: '注册申请管理',
        component: './SecurityManage/Register',
      },
    ],
  },
  {
    path: '/',
    redirect: '/dataResources/list',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
