export const saveToken = (token: string) => {
  localStorage.setItem('poly-bigData-token', token);
};

export const getToken = () => {
  return localStorage.getItem('poly-bigData-token');
};

export const clearToken = () => {
  localStorage.removeItem('poly-bigData-token');
};

const { protocol, host } = window.location;

export const isProduction = () => host.indexOf('poly.com.cn') >= 0;

export const UserApiUrl = isProduction() ? 'https://uc.poly.com.cn' : 'https://poly.iot.fit';

// 登录后的回调地址
export const callbackUrl = `${protocol}//${host}/user/uc-login`;

const host_uri = `${UserApiUrl}/sso/polyOAuth/authorize`;

export const client_id = isProduction() ? 'haSoSAllthymaOnzhpWn' : 'Aa2WaoSOShnhY2mpzy5h';

// 登录地址
export const login_uri = `${host_uri}?response_type=code&client_id=${client_id}&redirect_uri=${encodeURIComponent(
  callbackUrl,
)}&scope=openid`;

// 退出地址
export const logout_uri = `${UserApiUrl}/sso/polyOAuth/logout?client_id=${client_id}&post_logout_redirect_uri=${encodeURIComponent(
  login_uri,
)}`;
