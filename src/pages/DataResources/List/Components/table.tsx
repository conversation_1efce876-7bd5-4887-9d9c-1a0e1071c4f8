import { ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { ConfigProvider } from 'antd';
import { Application } from './aplication';
interface listTableProps {
  data: any[];
  setCurrentPage: (val: any) => void;
  setPageSize: (val: any) => void;
  pageSize: number;
  totalNum: number;
  currentPage: number;
  setCurrentSorter: (val: any) => void;
}

export function ListTable({
  data,
  setCurrentPage,
  pageSize,
  setPageSize,
  totalNum,
  currentPage,
  setCurrentSorter,
}: listTableProps) {
  const columns: any = [
    {
      title: '数据编号',
      dataIndex: 'num',
      ellipsis: true,
      key: 'num',
      width: 140,
    },
    {
      title: '数据资源中文名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 200,
    },
    {
      title: '归口部门/公司',
      dataIndex: 'owner_dept',
      ellipsis: true,
      key: 'owner_dept',
      width: 200,
    },
    {
      title: '资源归属分类',
      dataIndex: 'category',
      ellipsis: true,
      key: 'category',
      width: 120,
    },
    {
      title: '数据项数量',
      dataIndex: 'data_items_num',
      ellipsis: true,
      sorter: true,
      onHeaderCell: (column: any) => ({
        onClick: () => {
          switch (column?.title?.props?.title) {
            case '点击升序':
              setCurrentSorter('data_items_num_asc');
              break;
            case '点击降序':
              setCurrentSorter('data_items_num_desc');
              break;
            case '取消排序':
              setCurrentSorter('');
              break;
            default:
              break;
          }
          console.log(column?.title?.props?.title);
        },
      }),
      key: 'data_items_num',
      width: 120,
    },
    {
      title: '来源渠道',
      dataIndex: 'main_info',
      ellipsis: true,
      key: 'main_info',
      width: 120,
    },
    {
      title: '来源系统/报表',
      dataIndex: 'source_name',
      ellipsis: true,
      key: 'source_name',
      width: 160,
    },
    {
      title: '一级业务域',
      dataIndex: 'first_biz_domain',
      ellipsis: true,
      key: 'first_biz_domain',
      width: 120,
    },
    {
      title: '二级业务域',
      dataIndex: 'second_biz_domain',
      ellipsis: true,
      key: 'second_biz_domain',
      width: 120,
    },
    {
      title: '三级业务域',
      dataIndex: 'third_biz_domain',
      ellipsis: true,
      key: 'third_biz_domain',
      width: 120,
    },
    {
      title: '主要数据信息',
      dataIndex: 'data_fields',
      ellipsis: true,
      key: 'data_fields',
      width: 300,
    },
    {
      title: '数据安全等级',
      dataIndex: 'security_level',
      ellipsis: true,
      key: 'security_level',
      width: 120,
    },
    {
      title: '开放共享类型',
      dataIndex: 'share_type',
      ellipsis: true,
      key: 'share_type',
      width: 140,
    },
    {
      title: '敏感信息强调说明',
      dataIndex: 'sensitive_desc',
      ellipsis: true,
      key: 'sensitive_desc',
      width: 140,
    },
    {
      title: '敏感信息采集/共享策略',
      dataIndex: 'sensitive_strategy',
      ellipsis: true,
      key: 'sensitive_strategy',
      width: 120,
    },
    {
      title: '更新频率',
      dataIndex: 'update_frequency',
      ellipsis: true,
      key: 'update_frequency',
      width: 140,
    },
    {
      title: '业务规模',
      dataIndex: 'biz_scale',
      ellipsis: true,
      key: 'biz_scale',
      width: 140,
    },
    {
      title: '可用性说明',
      dataIndex: 'usability_desc',
      ellipsis: true,
      key: 'usability_desc',
      width: 140,
    },
    {
      title: '数据库/文件类型',
      dataIndex: 'db_type',
      ellipsis: true,
      key: 'db_type',
      width: 140,
    },
    {
      title: '所在库/服务器名称',
      dataIndex: 'db_name',
      ellipsis: true,
      key: 'db_name',
      width: 140,
    },
    {
      title: '所在表/路径名称',
      dataIndex: 'table_name',
      ellipsis: true,
      key: 'table_name',
      width: 140,
    },
    {
      title: '存储形式',
      dataIndex: 'storage_type',
      ellipsis: true,
      key: 'storage_type',
      width: 140,
    },
    {
      title: '负责人姓名',
      dataIndex: 'head_name',
      ellipsis: true,
      key: 'head_name',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'head_phone',
      ellipsis: true,
      key: 'head_phone',
      width: 160,
    },
    {
      title: '电子邮箱',
      dataIndex: 'head_email',
      ellipsis: true,
      key: 'head_email',
      width: 160,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      key: 'remark',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 160,
      dataIndex: 'action',
      render: (_: any, { operations, id }: any) => (
        <div className="text-[#002FA5] flex gap-[20px] text-[14px] font-normal leading-[19.6px] flex-wrap">
          {operations?.can_view && (
            <a
              className="hover:underline"
              onClick={() => history.push(`/dataResources/list/detail/${id}`)}
            >
              查看详情
            </a>
          )}
          {operations?.can_apply_use && <Application type="list" id={id} />}
          {operations?.can_update && (
            <a
              className="hover:underline"
              onClick={() => history.push(`/dataResources/report/apply?id=${id}`)}
            >
              编辑更改
            </a>
          )}
        </div>
      ),
    },
  ];

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBg: '#D9E0F2',
          },
        },
      }}
    >
      <ProTable
        columns={columns}
        dataSource={data}
        search={false}
        options={{ reload: false }}
        scroll={{ x: 1300 }}
        pagination={{
          total: totalNum,
          onChange: (page) => {
            setCurrentPage(page);
          },
          onShowSizeChange: (current, pageSize) => {
            setPageSize(pageSize);
          },
          pageSize,
          current: currentPage,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => (
            <>
              共 {totalNum} 条 本页显示第 {range[0]} ~ {range[1]} 条
            </>
          ),
        }}
      />
    </ConfigProvider>
  );
}
