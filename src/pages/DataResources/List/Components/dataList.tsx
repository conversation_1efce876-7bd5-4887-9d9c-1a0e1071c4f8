import { Table, Tabs } from 'antd';
import React from 'react';

interface DataType {
  field: string;
  cn_name: string;
  data_type: string;
  description: string;
  is_sensitive: boolean;
  sequence: number;
}

const TableList = ({ data = [] }: any) => {
  //page
  const [currentPage, setCurrentPage] = React.useState<any>(1);
  const [pageSize, setPageSize] = React.useState<number>(10);
  const columns: any['columns'] = [
    {
      title: '序号',
      dataIndex: 'sequence',
      key: 'sequence',
      width: '6%',
    },
    {
      title: '字段名',
      dataIndex: 'field',
      key: 'field',
    },
    {
      title: '中文名称',
      dataIndex: 'cn_name',
      key: 'cn_name',
    },
    {
      title: '字段描述',
      key: 'description',
      dataIndex: 'description',
    },
    // {
    //   title: '字段类型',
    //   key: 'data_type',
    //   dataIndex: 'data_type',
    // },
    {
      title: '敏感状态',
      key: 'is_sensitive',
      dataIndex: 'is_sensitive',
      render: (_: any, record: any) => (record.is_sensitive ? '敏感' : '不敏感'),
    },
  ];

  return (
    <div>
      <div className="mb-[10px]">
        共 <span className="text-[red]">{data.length}</span> 条数据
      </div>
      <Table<DataType>
        columns={columns}
        dataSource={data}
        pagination={{
          total: data.length,
          onChange: (page) => {
            setCurrentPage(page);
          },
          onShowSizeChange: (current, pageSize) => {
            setPageSize(pageSize);
          },
          pageSize,
          current: currentPage,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => (
            <>
              共 {data.length} 条 本页显示第 {range[0]} ~ {range[1]} 条
            </>
          ),
        }}
      />
    </div>
  );
};

function DetailDataList({ data }: any) {
  const items: any['items'] = [
    {
      key: '1',
      label: '字段信息',
      children: <TableList data={data?.data_items && data?.data_items} />,
    },
  ];
  return (
    <div>
      <Tabs defaultActiveKey="1" items={items} />
    </div>
  );
}
export default DetailDataList;
