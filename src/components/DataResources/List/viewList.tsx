import { EllipsisOutlined } from '@ant-design/icons';
import { Dropdown } from 'antd';

export function ViewList({ data = [], changeView, type }: any) {
  return (
    <div className="grid grid-cols-3 gap-16">
      {data.map((item: any, index: number) => {
        return (
          // <Card
          //   key={index}
          //   title={
          //     <div className="flex items-center py-4 font-medium text-[36px]">
          //       <div>
          //         <img src={listTitleIcon} className="w-8" />
          //       </div>
          //       <Tooltip title={item.name}>
          //         <span className="font-medium text-[20px] ml-4 truncate" title={item.name}>
          //           {item.name}
          //         </span>
          //       </Tooltip>
          //     </div>
          //   }
          //   //style={{ width: 330 }}
          // >
          //   <div className="flex items-center justify-center">
          //     <Progress
          //       type="dashboard"
          //       gapDegree={100}
          //       percent={(item.collected / item.total) * 100}
          //       steps={{ count: 30, gap: 3 }}
          //       strokeColor="#002FA5"
          //       size={200}
          //       format={() => (
          //         <div>
          //           <div>
          //             <span className="text-[48px] font-semibold text-[#002FA5]">
          //               {item.collected}
          //             </span>
          //             <span className="text-[24px] font-normal text-black"> / {item.total}</span>
          //           </div>
          //           <div className="text-[14px] font-normal leading-[22px] text-[#00000073] mt-[6px]">
          //             已创建采集的数据表
          //           </div>
          //         </div>
          //       )}
          //       trailColor="rgba(0, 47, 165, 0.1)"
          //       strokeWidth={12}
          //     />
          //   </div>
          //   <a className="" onClick={() => changeView('资源列表', type, item.name)}>
          //     查看详情
          //   </a>
          // </Card>
          <div
            key={index}
            className="border-[1px] border-[#CBD5ED] rounded-[10px] p-[20px] flex flex-col justify-between items-start gap-2"
          >
            <div className="w-[100%] flex justify-between items-center">
              <div className="font-medium text-[14px] truncate">{item?.name}</div>
              <Dropdown
                trigger={['click']}
                placement="bottomRight"
                dropdownRender={() => {
                  return (
                    <a
                      style={{ color: 'rgba(0, 47, 165, 1)' }}
                      onClick={() => changeView('资源列表', type, item.name)}
                    >
                      查看详情
                    </a>
                  );
                }}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <EllipsisOutlined style={{ fontSize: '24px' }} />
                </a>
              </Dropdown>
            </div>
            <div className="flex items-baseline gap-2">
              <span className="font-medium text-[32px]">{item?.total}</span>
              <span className="font-normal text-[14px]">月度更新率: {item?.wow}%</span>
            </div>
            <div className="flex gap-1 items-center">
              <div
                className="w-[8px] h-[8px] rounded-[50%]"
                style={{ background: 'rgba(0, 47, 165, 1)' }}
              ></div>
              <span className="font-normal text-[14px] text-[#949596]">已录入资源数</span>
            </div>
          </div>
        );
      })}
    </div>
  );
}
