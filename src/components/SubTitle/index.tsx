import { ReactNode } from 'react';

interface SubTitlePropsBase {
  title?: string;
  children?: ReactNode;
}

interface SubTitlePropsWithTitle extends SubTitlePropsBase {
  title: string;
  children?: never;
}

interface SubTitlePropsWithChildren extends SubTitlePropsBase {
  title?: never;
  children: ReactNode;
}

type SubTitleProps = SubTitlePropsWithTitle | SubTitlePropsWithChildren;

export const SubTitle = ({ title, children }: SubTitleProps) => {
  return (
    <div className="my-4 flex items-center gap-2">
      <div className="bg-[#002FA5] h-7 w-1"></div>
      <div className="text-sm font-medium">{title || children}</div>
    </div>
  );
};

SubTitle.displayNamee = 'SubTitle';
