import { ReactComponent as ReportIcon } from '@/assets/report_icon.svg';
import { GetCollectionStatus } from '@/services/DataAggregation';
import { Button, ConfigProvider, message, Table } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation } from 'umi';
import dayjs from 'dayjs';

const CollectionStatus = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);

  const columns = [
    { title: '二级公司', dataIndex: 'company_name' },
    { title: '汇聚领域', dataIndex: 'area' },
    { title: '已上报报表数量', dataIndex: 'table_count' },
    {
      title: '最近上报时间',
      dataIndex: 'reported_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '最近更新时间',
      dataIndex: 'updated_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_: any, record: any) => (
        <Button
          type="link"
          href={`/dataResources/dataAggregation/collectionInfo?company_code=${record.company_code}&area=${record.area}`}
          rel="noopener noreferrer"
        >
          查看采集详情
        </Button>
      ),
    },
  ];

  const getTableData = async () => {
    setLoading(true);

    try {
      const res = await GetCollectionStatus({
        area: queryParams.get('area'),
        use_total_num: 1,
      });

      if (res.code !== 200001) {
        setTableData([]);
        message.error(res.message);
        return;
      }

      setTableData(res.data.data.data);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!queryParams.get('area')) {
      return;
    }

    getTableData();
  }, [queryParams.get('area')]);

  return (
    <>
      <h1 className="flex gap-x-4 items-center justify-center my-16">
        <ReportIcon height="40px" width="40px" />
        <span className="text-3xl font-medium">数据汇聚</span>
      </h1>

      <div className="border-[#F0F0F0] rounded-3xl p-6 border">
        <ConfigProvider
          theme={{
            components: {
              Table: {
                headerBg: '#D9E0F2',
              },
            },
          }}
        >
          <Table
            columns={columns}
            dataSource={tableData}
            loading={loading}
            scroll={{ x: 'max-content' }}
            rowKey="company_code"
            pagination={{
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              defaultPageSize: 10,
              showQuickJumper: true,
            }}
          />
        </ConfigProvider>
      </div>
    </>
  );
};

export default CollectionStatus;
