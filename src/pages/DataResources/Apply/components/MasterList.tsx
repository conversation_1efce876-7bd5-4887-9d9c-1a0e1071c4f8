import {
  getApplyMasterData<PERSON><PERSON>,
  getApplyMasterDataList,
  ResourceUseApplicationListOperations,
  ResourceUseListItem,
  ResourceUseVettingListOperations,
} from '@/services/dataResources/applyApi';
import { ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Space, Typography } from 'antd';
import { useState } from 'react';
import { MasterDataProcess } from './MasterDataProcess';
import { VettingDetail } from './VettingDetail';
import { MasterDataVettingProcess } from './MasterDataVettingProcess';

const { Text } = Typography;

interface MasterListProps {
  type: string;
}

export const MasterList = ({ type }: MasterListProps) => {
  const [currentItem, setCurrentItem] = useState<ResourceUseListItem>();
  const [isShowVettingDetail, setIsShowVettingDetail] = useState(false);
  const [isShowVettingProcess, setIsShowVettingProcess] = useState(false);
  const [isShowApplicationModify, setIsShowApplicationModify] = useState(false);
  const [loading, setLoading] = useState(false);
  const [apiData, setApiData] = useState({
    url: '',
    token: '',
    macs: [],
  });

  const [isApiModalOpen, setIsApiModalOpen] = useState(false);

  const showApiModal = async (id: string) => {
    setIsApiModalOpen(true);
    setLoading(true);
    try {
      const { code, data, message } = await getApplyMasterDataApi(id);
      if (code !== 200001) {
        message.error(message);
        return;
      }
      if (data.url) {
        data.url = `https://bigdata.poly.com.cn${data.url}`;
      }
      setApiData(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleVettingDetailClick = (item: ResourceUseListItem) => {
    setCurrentItem(item);
    setIsShowVettingDetail(true);
  };

  const handleVettingProcessClick = (item: ResourceUseListItem) => {
    setCurrentItem(item);
    setIsShowVettingProcess(true);
  };

  const handleApplicationModifyClick = (item: ResourceUseListItem) => {
    setCurrentItem(item);
    setIsShowApplicationModify(true);
  };

  const columns: any['columns'] = [
    {
      title: '主数据名称',
      dataIndex: 'major_data_name',
      key: 'major_data_name',
    },
    {
      title: '申请人',
      dataIndex: 'applicant_name',
      key: 'applicant_name',
    },
    {
      title: '数据所属公司 / 部门',
      dataIndex: 'owner_dept',
      key: 'owner_dept',
    },
    {
      title: '申请状态',
      key: 'status_str',
      dataIndex: 'status_str',
    },
    {
      title: '申请时间',
      dataIndex: 'applicant_date',
      key: 'applicant_date',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          {type === '0' &&
            (record?.operations as ResourceUseApplicationListOperations)?.can_update && (
              <Button
                type="link"
                className="px-0"
                onClick={() => handleApplicationModifyClick(record)}
              >
                修订申请
              </Button>
            )}
          {type === '1' &&
            (record?.operations as ResourceUseVettingListOperations)?.can_approve && (
              <Button
                type="link"
                className="px-0"
                onClick={() => handleVettingProcessClick(record)}
              >
                处理审批
              </Button>
            )}
          {record?.operations?.can_view_approval && (
            <Button type="link" className="px-0" onClick={() => handleVettingDetailClick(record)}>
              审批详情
            </Button>
          )}
          {record?.status === 6 && type === '0' && (
            <Button type="link" className="px-0" onClick={() => showApiModal(record.apply_id)}>
              查看api
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      {/* 查看 api */}
      <Modal
        title="查看Api"
        open={isApiModalOpen}
        loading={loading}
        onOk={() => setIsApiModalOpen(false)}
        cancelButtonProps={{
          style: {
            display: 'none',
          },
        }}
        width={680}
        onCancel={() => setIsApiModalOpen(false)}
      >
        <div style={{ margin: '15px 0', display: 'flex' }}>
          <Text strong style={{ marginRight: '8px', color: '#555', minWidth: 'max-content' }}>
            请求地址:
          </Text>
          <Text
            copyable={{
              text: apiData.url,
              onCopy: () => message.success('请求地址已复制成功！'),
            }}
          >
            <span style={{ color: '#1890ff' }}>{apiData.url}</span>
          </Text>
        </div>
        <div>
          <Text strong style={{ marginRight: '8px', color: '#555' }}>
            token:
          </Text>
          <Text
            copyable={{
              text: apiData.token,
              onCopy: () => message.success('token已复制成功！'),
            }}
          >
            <span style={{ color: '#555' }}>{apiData.token}</span>
          </Text>
        </div>
        <div>
          <Text strong style={{ marginRight: '8px', color: '#555' }}>
            macs:
          </Text>
          <Text
            copyable={{
              text: apiData?.macs?.join(','),
              onCopy: () => message.success('macs已复制成功！'),
            }}
          >
            <span style={{ color: '#555' }}>{apiData?.macs?.join(',')}</span>
          </Text>
        </div>
      </Modal>

      <Modal
        maskClosable={false}
        onCancel={() => setIsShowVettingDetail(false)}
        onOk={() => {}}
        open={isShowVettingDetail}
        title="审批详情"
        width={600}
        footer={[
          <div key="footer" className="text-right">
            <Button type="primary" onClick={() => setIsShowVettingDetail(false)}>
              确认
            </Button>
          </div>,
        ]}
      >
        <VettingDetail id={currentItem?.apply_id} isMasterData={true} />
      </Modal>

      <Modal
        maskClosable={false}
        onCancel={() => setIsShowVettingProcess(false)}
        open={isShowVettingProcess}
        title="主数据使用申请审批"
        width={800}
        footer={null}
      >
        <MasterDataProcess
          id={currentItem?.apply_id}
          setIsShowModal={setIsShowVettingProcess}
          type="1"
          // isMasterData={true}
        />
      </Modal>

      <Modal
        maskClosable={false}
        onCancel={() => setIsShowApplicationModify(false)}
        open={isShowApplicationModify}
        title="主数据使用申请修改"
        width={800}
        footer={null}
      >
        <MasterDataVettingProcess
          id={currentItem?.apply_id}
          setIsShowModal={setIsShowApplicationModify}
        />
      </Modal>
      <ProTable<any>
        columns={columns}
        params={{ type: type }}
        rowKey="id"
        search={false}
        request={async (params: any) => {
          const { data, message, code } = await getApplyMasterDataList(params);
          if (code !== 200001) {
            message.error(message);
          }
          return {
            data: data.list || [],
            total: data.total || 0,
            success: true,
          };
        }}
        pagination={{
          showTotal: (total, range) => `共 ${total} 条 本页显示第${range[0]}~${range[1]}条`,
        }}
        options={false}
      />
    </>
  );
};
