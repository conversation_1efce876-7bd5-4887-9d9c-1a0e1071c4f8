import { ReactComponent as ReportIcon } from '@/assets/report_icon.svg';
import { ViewList } from '@/components/DataResources/List/viewList';
import { SearchBox } from '@/components/SearchBox';
import { GetListData, GetStatisticsData, GetMajorDataList } from '@/services/dataResources/listApi';
import { getOrgsTree } from '@/services/dataResources/orgsApi';
import { Button, ConfigProvider, message, Segmented, Spin } from 'antd';
import cn from 'classnames';
import React, { useEffect, useState } from 'react';
import { createSearchParams, history, useLocation } from 'umi';
import { MajorDataApplication } from './Components/majorDataApplication';
import { MajorDataDetail } from './Components/majorDataDetail';
import { ListTable } from './Components/table';

type View = '资源列表' | '资源统计' | '主数据';
type deepView = '业务视角' | '来源视角' | '公司视角';

const searchTypeOptions = [
  { label: '表名', key: 'name' },
  { label: '主要数据信息', key: 'main_info' },
  { label: '来源系统/报表', key: 'source_name' },
  { label: '负责人', key: 'head_name' },
];

function List() {
  const [viewValue, setViewValue] = React.useState<View>('资源列表');
  const [deepViewValue, setDeepViewValue] = React.useState<deepView>('业务视角');
  //默认搜索条件
  const [defaultSearchValue, setDefaultSearchValue] = React.useState<any>({});
  //搜索条件
  const [searchValue, setSearchValue] = React.useState<any>({});
  //input搜索条件
  const [searchInputValue, setSearchInputValue] = React.useState<any>({});
  //page
  const [currentPage, setCurrentPage] = React.useState<any>(1);
  const [pageSize, setPageSize] = React.useState<number>(10);
  //数据排序
  const [dataSorter, setDataSorter] = React.useState<string>('');
  //listData
  const [listData, setListData] = React.useState<any>([]);
  //listData
  const [statisticsData, setStatisticsData] = React.useState<any>([]);
  //加载
  const [loading, setLoading] = React.useState<any>(false);

  //公司列表
  const [companyList, setCompanyList] = useState<any>([]);
  const setPage = (val: any) => {
    setCurrentPage(val);
  };
  //设置排序方式
  const setListSorter = (val: any) => {
    setDataSorter(val);
  };

  const location = useLocation();
  const queryParams = createSearchParams(location.search);
  const majorValue = queryParams.get('major');
  const majorDetail = queryParams.get('detail');

  const [currentMajorDataItem, setCurrentMajorDataItem] = useState<any>(null);
  const [isMajorDataApplyModalVisible, setIsMajorDataApplyModalVisible] = useState<boolean>(false);
  const [majorDataDetailModalVisible, setMajorDataDetailModalVisible] = useState<boolean>(false);

  const [majorDataListData, setMajorDataListData] = useState<any>([]);

  const getMajorDataList = async () => {
    setLoading(true);
    try {
      const res = await GetMajorDataList();
      if (res.code === 200001) {
        setMajorDataListData(res.data);
        return;
      }
      message.error(res.message);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  const getListValue = async () => {
    setLoading(true);
    const sendValue = {
      ...searchInputValue,
      current: currentPage,
      sort: dataSorter,
      pageSize,
      // num: 0,
      // table_name: 0,
      my_manage: searchValue[0] ? true : null,
      amount_range: searchValue?.amount_range ? searchValue?.amount_range[0] : null,
      first_biz_domain: searchValue?.first_biz_domain ? searchValue?.first_biz_domain[0] : null,
      update_frequency: searchValue?.update_frequency ? searchValue?.update_frequency[0] : null,
      owner_dept_id: searchValue?.owner_dept_id ? searchValue?.owner_dept_id[0] : null,
      second_biz_domain: searchValue?.second_biz_domain ? searchValue?.second_biz_domain : null,
      third_biz_domain: searchValue?.third_biz_domain ? searchValue?.third_biz_domain[0] : null,
      db_type: searchValue?.db_type ? searchValue?.db_type[0] : null,
      security_level: searchValue?.security_level ? searchValue?.security_level[0] : null,
      share_type: searchValue?.share_type ? searchValue?.share_type[0] : null,
      source_channel: searchValue?.source_channel ? searchValue?.source_channel[0] : null,
    };
    const res = await GetListData(sendValue);
    if (res.code === 200001) {
      setListData(res.data);
      setLoading(false);
    } else {
      message.error(res.message);
      setLoading(false);
    }
  };
  //资源统计
  const getStatisticsValue = async () => {
    if (viewValue === '资源列表') {
      return;
    }
    setLoading(true);
    const res = await GetStatisticsData({
      type:
        deepViewValue === '业务视角'
          ? 0
          : deepViewValue === '来源视角'
          ? 1
          : deepViewValue === '公司视角'
          ? 2
          : deepViewValue === '数据项统计'
          ? 3
          : 0,
    });
    if (res.code === 200001) {
      setStatisticsData(res.data);
      setLoading(false);
    } else {
      message.error(res.message);
      setLoading(false);
    }
  };

  //匹配公司 id
  const findIdByName = (data: any, name: any) => {
    for (const item of data) {
      if (item.name === name) {
        return item.id;
      }
      if (item.children) {
        const result = findIdByName(item.children, name);
        if (result) {
          return result;
        }
      }
    }
    return null; // 未找到
  };

  //资源统计查看详情
  const changeView = (view: any, type: any, search: any) => {
    setViewValue(view);
    if (type === '业务视角') {
      setDefaultSearchValue({ first_biz_domain: [search] });
    }
    if (type === '来源视角') {
      setDefaultSearchValue({ source_channel: [search] });
    }
    if (type === '公司视角') {
      setDefaultSearchValue({ owner_dept_id: [findIdByName(companyList, search)] });
    }
    if (type === '数据项统计') {
      setDefaultSearchValue({ owner_dept_id: [findIdByName(companyList, search)] });
    }
  };

  //获取公司列表
  const getCompanyId = async () => {
    const response = await getOrgsTree();
    if (response.code === 200001) {
      setCompanyList(response.data);
    }
  };

  //资源列表
  useEffect(() => {
    getListValue();
  }, [currentPage, searchValue, pageSize, searchInputValue, dataSorter]);
  //资源统计
  useEffect(() => {
    getStatisticsValue();
  }, [deepViewValue, viewValue]);

  useEffect(() => {
    getCompanyId();
    getMajorDataList();
  }, []);

  useEffect(() => {
    if (majorValue) {
      setViewValue('主数据');
      const majorDataItem = majorDataListData.find((item) => item.name === majorValue);
      if (majorDataItem) {
        setCurrentMajorDataItem(majorDataItem);
        setIsMajorDataApplyModalVisible(true);
      }
    }

    if (majorDetail) {
      setViewValue('主数据');
      const majorDataItem = majorDataListData.find((item) => item.name === majorDetail);
      if (majorDataItem) {
        setCurrentMajorDataItem(majorDataItem);
      }
      setMajorDataDetailModalVisible(true);
    }
  }, [majorDataListData]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Segmented: {
            itemSelectedBg: '#002FA5',
            itemSelectedColor: '#fff',
            itemHoverColor: '#fff',
            itemHoverBg: 'rgba(0, 47, 165, 0.15)',
          },
          List: {
            metaMarginBottom: '0px',
          },
          Table: {
            headerBg: '#D9E0F2',
          },
        },
      }}
    >
      <div>
        <h1 className="flex gap-x-4 items-center justify-center">
          <ReportIcon height="40px" width="40px" />
          <span className="text-3xl font-medium">原始数据资源目录</span>
        </h1>
        <div className="mt-5">
          <div className="my-5 flex items-center">
            <div className="space-x-2 flex-1">
              <Segmented
                size="large"
                style={{ border: '1px solid #D9D9D9' }}
                defaultValue="资源列表"
                onChange={(value) => setViewValue(value as View)}
                value={viewValue}
                options={['资源列表', '资源统计', '主数据']}
              />
            </div>
            {viewValue === '资源列表' && (
              <div className="flex-shrink-0 flex-grow-0">
                <Button type="primary" onClick={() => history.push('/dataResources/report/apply')}>
                  数据上报
                </Button>
              </div>
            )}
          </div>
        </div>
        <div
          className={cn('border-[1px] border-[#DEDEDE] rounded-[20px] p-[20px]', {
            'border-none p-0': viewValue === '资源统计',
          })}
        >
          {viewValue === '资源列表' && (
            <SearchBox
              searchTypeOptions={searchTypeOptions}
              defaultSelected={defaultSearchValue}
              extraFilterOptions={[
                {
                  label: '我管理的',
                  key: '0',
                  options: [{ label: '我管理的', key: 'wgld' }],
                },
              ]}
              addonInputBefore={(updateSelected, isSelected) => {
                return (
                  <Button
                    className={cn('mr-5 cursor-pointer hover:bg-[#002fa50d]', {
                      'text-[#002fa5] border-[#002fa5]': isSelected(
                        {
                          label: '我管理的',
                          key: '0',
                          options: [{ label: '我管理的', key: 'wgld' }],
                        },
                        'wgld',
                      ),
                    })}
                    onClick={() =>
                      updateSelected(
                        {
                          label: '我管理的',
                          key: '0',
                          options: [{ label: '我管理的', key: 'wgld' }],
                        },
                        'wgld',
                      )
                    }
                  >
                    我管理的
                  </Button>
                );
              }}
              onSearchChange={(search, searchType) => {
                setSearchInputValue({ [searchType]: search });
              }}
              onFilterChange={(filtersObj, filtersArr) => {
                setCurrentPage(1);
                setSearchValue(filtersObj);
                console.log(filtersArr);
              }}
            />
          )}
          {viewValue === '资源统计' && (
            <div className="mt-5 flex items-center">
              <Segmented
                size="middle"
                style={{ border: '1px solid #D9D9D9' }}
                defaultValue={deepViewValue}
                onChange={(value) => setDeepViewValue(value as deepView)}
                options={['业务视角', '来源视角', '公司视角', '数据项统计']}
              />
            </div>
          )}
          <div className="mt-5">
            {viewValue === '资源列表' && (
              <Spin spinning={loading}>
                <ListTable
                  currentPage={currentPage}
                  data={listData?.list}
                  pageSize={pageSize}
                  setCurrentSorter={setListSorter}
                  setCurrentPage={setPage}
                  setPageSize={setPageSize}
                  totalNum={listData?.total}
                />
              </Spin>
            )}
            {viewValue === '资源统计' && (
              <div>
                <Spin spinning={loading}>
                  <ViewList data={statisticsData} changeView={changeView} type={deepViewValue} />
                </Spin>
              </div>
            )}
            {viewValue === '主数据' && (
              <div>
                <div>
                  <iframe
                    allowFullScreen
                    title="主数据仪表盘"
                    className="w-full h-[75vh] border-none"
                    src="https://bigdata.poly.com.cn/dash/integration/1?rid=8&hideTitle=true&hidePageBar=true&token=1750657884036000f5d2c8018f08694b8501a"
                  />
                </div>
                <MajorDataApplication
                  majorDataId={currentMajorDataItem?.id}
                  majorDataName={currentMajorDataItem?.name}
                  approvalType={currentMajorDataItem?.approval_type}
                  isModalOpen={isMajorDataApplyModalVisible}
                  setIsModalOpen={setIsMajorDataApplyModalVisible}
                />

                <MajorDataDetail
                  isModalOpen={majorDataDetailModalVisible}
                  majorDataId={currentMajorDataItem?.id}
                  majorDataName={currentMajorDataItem?.name}
                  setIsModalOpen={setMajorDataDetailModalVisible}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
}

export default List;
