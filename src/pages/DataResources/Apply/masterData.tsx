import { useModel } from '@umijs/max';
import { Button } from 'antd';
import { useState } from 'react';
import { MasterList } from './components/MasterList';

function MasterData() {
  const { initialState } = useModel('@@initialState');
  const [type, setType] = useState('0');

  return (
    <div>
      <div className="flex items-center">
        <div className="space-x-2 flex-1">
          <Button onClick={() => setType('0')} type={type === '0' ? 'primary' : 'default'}>
            我申请的
          </Button>
          {initialState?.currentUser?.roles[0] !== 'user' && (
            <Button onClick={() => setType('1')} type={type === '1' ? 'primary' : 'default'}>
              待我审批
            </Button>
          )}
        </div>
      </div>
      <div className="mt-5">
        <MasterList type={type} />
      </div>
    </div>
  );
}
export default MasterData;
