import { request } from 'umi';

export interface ResourceUseResponse<T> {
  code: number;
  data: T;
  message: string;
}

export interface ResourceUseApplicationListOperations {
  // 补充申请
  can_supplement: boolean;
  // 修订申请
  can_update: boolean;
  // 审批详情
  can_view_approval: boolean;
  // 资源详情
  can_view_resource: boolean;
}
export interface ResourceUseVettingListOperations {
  // 处理审批
  can_approve: boolean;
  // 审批详情
  can_view_approval: boolean;
  // 资源详情
  can_view_resource: boolean;
}
export interface ResourceUseListItem {
  // 数据量
  amount?: number;
  // 使用申请 id
  apply_id?: string;
  approval_status?: number;
  // 审批状态
  approval_status_str?: string;
  // 业务规模
  biz_scale?: string;
  // 一级业务域
  first_biz_domain?: string;
  // 负责人姓名
  head_name?: string;
  // 资源 id
  id?: string;
  // 主要数据信息
  main_info?: string;
  // 资源名称
  name?: string;
  // 资源编号
  num?: string;
  // 操作权限
  operations?: ResourceUseApplicationListOperations | ResourceUseVettingListOperations;
  // 拥有部门
  owner_dept?: string;
  // 拥有部门ID
  owner_dept_id?: string;
  // 二级业务域
  second_biz_domain?: string;
  // 数据安全等级
  security_level?: string;
  // 敏感信息强调说明
  sensitive_desc?: string;
  // 敏感信息采集或共享策略
  sensitive_strategy?: string;
  // 开放共享类型
  share_type?: string;
  // 来源渠道类别
  source_channel?: string;
  // 来源系统或报表名称
  source_name?: string;
  status?: number;
  // 状态
  status_str?: string;
  // 三级业务域
  third_biz_domain?: string;
  // 更新频率
  update_frequency?: string;
}
export interface ResourceUseListData {
  // 列表
  list: ResourceUseListItem[];
  // 总数
  total: number;
}

export interface ResourceUseListParams {
  // 数据量
  amount_range?: string;
  // 当前页
  current: number;
  // 数据库类型
  db_type?: string;
  // 一级业务域
  first_biz_domain?: string;
  // 资源名称
  name?: string | number;
  // 资源编号
  num?: string;
  // 归口部门 id
  owner_dept_id?: string;
  // 每页的数据量
  pageSize: number;
  // 二级业务域
  second_biz_domain?: string;
  // 安全等级
  security_level?: string;
  // 共享类型
  share_type?: string;
  // 数据表名称
  table_name?: string;
  // 三级业务域
  third_biz_domain?: string;
  // 0：我申请的，1：待我审批的
  type: string;
  // 更新频率
  update_frequency?: string;
  [property: string]: any;
}

export interface ResourceUseListItemApplicationDetail {
  // 申请人单位名称
  applicant_company: string;
  // 申请日期
  applicant_date: string;
  // 申请人部门名称
  applicant_dept: string;
  // 申请人联系方式
  applicant_mobile: string;
  // 申请人姓名
  applicant_name: string;
  // 申请人职位
  applicant_position: string;
  // 采集频率
  collect_frequency: string;
  // 采集时段
  collect_period: string;
  // 是否同意
  is_agree: boolean;
  // 备注
  remark: string;
  // 需求描述
  req_desc: string;
  resource_id: string;
  // 资源名称
  resource_name: string;
  // 资源编号
  resource_num: string;
  // 数据安全措施
  security_step: string;
  // 数据存储和处理位置
  store_location: string;
  // 预期使用结束时间
  use_end_at: string;
  // 预期使用开始时间
  use_start_at: string;
}

export interface ResourceUseListItemVettingDetail {
  // 审批意见
  advice: string;
  // 审批 id
  approval_id: string;
  // 审批时间
  approved_at: string;
  // 审批人
  approver: string;
  // 审批人手机号
  approver_mobile: string;
  // 审批人职位
  approver_position: string;
  // 当前节点
  current_node: string;
  // 状态
  status: string;
}

export interface ResourceUseAppListItemModifyApplicationParams {
  // 采集频率
  collect_frequency: string;
  // 采集时段
  collect_period: string;
  // 是否同意
  is_agree: boolean;
  // 备注
  remark?: string;
  // 需求描述
  req_desc: string;
  // 数据安全措施
  security_step?: string;
  // 数据存储和处理位置
  store_location: string;
  // 预期使用结束时间
  use_end_at: string;
  // 预期使用开始时间
  use_start_at: string;
}

export interface ResourceUseListItemSubmitVettingProcessParams {
  // 审批意见
  advice: string;
  // 是否通过
  pass: boolean;

  [property: string]: string | boolean;
}

export interface ResourceUseListItemVettingProcess {
  // 审批意见
  advice: string;
  // 申请人单位名称
  applicant_company: string;
  // 申请日期
  applicant_date: string;
  // 申请人部门名称
  applicant_dept: string;
  // 申请人联系方式
  applicant_mobile: string;
  // 申请人姓名
  applicant_name: string;
  // 申请人职位
  applicant_position: string;
  // 审批 id
  approval_id: string;
  // 审批时间
  approved_at: string;
  // 审批人
  approver: string;
  // 审批人手机号
  approver_mobile: string;
  // 审批人职位
  approver_position: string;
  // 采集频率
  collect_frequency: string;
  // 采集时段
  collect_period: string;
  // 当前审批节点ID
  current_log_id: string;
  // 当前审批节点
  current_node: string;
  // 是否同意
  is_agree: boolean;
  // 是否通过
  pass: boolean;
  // 备注
  remark: string;
  // 需求描述
  req_desc: string;
  // 资源ID
  resource_id: string;
  // 资源名称
  resource_name: string;
  // 资源编号
  resource_num: string;
  // 数据安全措施
  security_step: string;
  // 数据存储和处理位置
  store_location: string;
  // 预期使用结束时间
  use_end_at: string;
  // 预期使用开始时间
  use_start_at: string;
  [property: string]: any;
}

export function getResourceUseListData(params: Partial<ResourceUseListParams>) {
  return request('/api/v1/resource-use', { params });
}

export function getResourceUseListItemVettingDetail(id: string) {
  return request(`/api/v1/resource-use/${id}/approval-detail`);
}

export function getResourceUseListItemVettingProcess(id: string) {
  return request(`/api/v1/resource-use/${id}/approval-page`);
}

export function submitResourceUseListItemVettingProcess(
  id: string,
  params: ResourceUseListItemSubmitVettingProcessParams,
) {
  return request(`/api/v1/resource-use/${id}/approval`, {
    method: 'POST',
    data: params,
  });
}

export function modifyResourceUseListItemApplication(
  id: string,
  params: ResourceUseAppListItemModifyApplicationParams,
) {
  return request(`/api/v1/resource-use/${id}`, {
    method: 'PUT',
    data: params,
  });
}

export function getResourceUseListItemApplicationDetail(id: string) {
  return request(`/api/v1/resource-use/${id}`);
}

//获取主数据列表
export function getApplyMasterDataList(data: { type: string; current: string; pageSize: string }) {
  return request(`/api/v1/major-data-use`, {
    method: 'GET',
    params: data,
  });
}

//主数据获取api
export function getApplyMasterDataApi(id: string) {
  return request(`/api/v1/major-data-use/${id}/api-detail`, {
    method: 'GET',
  });
}

//主数据审批详情
export function getApplyMasterDataApprovalDetail(id: string) {
  return request(`/api/v1/major-data-use/${id}/approval-detail`, {
    method: 'GET',
  });
}

//主数据审批处理详情
export function getApplyMasterDataApprovalPage(id: string) {
  return request(`/api/v1/major-data-use/${id}/approval-page`, {
    method: 'GET',
  });
}

//主数据修订详情
export function getApplyMasterDataDetail(id: string) {
  return request(`/api/v1/major-data-use/${id}`, {
    method: 'GET',
  });
}

//主数据 update
export function postApplyMasterDataUpdate(id: string, data: any) {
  return request(`/api/v1/major-data-use/${id}`, {
    method: 'PUT',
    data: data,
  });
}

//主数据审核
export function postApplyMasterDataApproval(id: string, data: any) {
  return request(`/api/v1/major-data-use/${id}/approval`, {
    method: 'POST',
    data: data,
  });
}
