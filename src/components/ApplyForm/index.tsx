import type { ColProps, FormProps, RowProps } from 'antd';
import { Form } from 'antd';
import { ReactNode, createContext } from 'react';
import styles from './index.module.less';

import { ApplyFormApplicant } from './Applicant';
import { ApplyFormApprovalOpinion } from './ApprovalOpinion';
import { ApplyFormBase } from './Base';
import { ApplyFormBusiness } from './Business';
import { ApplyFormManagent } from './Managent';
import { ApplyFormTech } from './Tech';

interface ApplyFormBaseProps {
  labelWidth?: string | number;
  itemSpan?: ColProps['span'];
  itemGutter?: RowProps['gutter'];
}

type ApplyFormProps = FormProps & ApplyFormBaseProps & { children: ReactNode };

export const Context = createContext<ApplyFormBaseProps>({});

export const ApplyForm = ({
  children,
  labelWidth,
  itemGutter = 12,
  itemSpan = 12,
  ...props
}: ApplyFormProps) => {
  let labelProps: FormProps = {};
  if (labelWidth) {
    labelProps = {
      labelCol: { flex: labelWidth },
      wrapperCol: { flex: 1 },
      labelWrap: true,
    };
  }
  return (
    <Context.Provider value={{ itemGutter, itemSpan, labelWidth }}>
      <Form {...props} {...labelProps} className={styles['apply-form']}>
        {children}
      </Form>
    </Context.Provider>
  );
};

ApplyForm.displayName = 'ApplyForm';

ApplyForm.Applicant = ApplyFormApplicant;
ApplyForm.ApprovalOpinion = ApplyFormApprovalOpinion;
ApplyForm.Base = ApplyFormBase;
ApplyForm.Business = ApplyFormBusiness;
ApplyForm.Managent = ApplyFormManagent;
ApplyForm.Tech = ApplyFormTech;
