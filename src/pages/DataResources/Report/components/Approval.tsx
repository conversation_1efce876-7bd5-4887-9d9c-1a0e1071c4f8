import { ApplyForm } from '@/components/ApplyForm';
import { SubTitle } from '@/components/SubTitle';
import { getOrgs, OrgsData, ResponseData } from '@/services/dataResources/orgsApi';
import { getApprovalPage } from '@/services/dataResources/reportApi';
import { Form, message, Spin } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

export const Approval = forwardRef(({ id }: { id?: string }, ref) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [ownerDeptOptions, setOwnerDeptOptions] = useState<{ label: string; value: string }[]>([]);
  const [showAdvice, setShowAdvice] = useState(false);
  const [showOwnerDeptDesc, setShowOwnerDeptDesc] = useState(false);
  const [showSensitiveStrategyDesc, setShowSensitiveStrategyDesc] = useState(false);
  useEffect(() => {
    if (!id) return;
    setLoading(true);
    const fetchData = async () => {
      try {
        const response = await getApprovalPage({ id });
        if (response.code !== 200001) {
          message.error(response.message);
          form.resetFields();
          return;
        }
        const newData = {
          ...response.data,
        };
        if (!newData.owner_dept_id && newData.owner_dept_detail) {
          newData.owner_dept_id = 'other';
          setShowOwnerDeptDesc(true);
        }
        if (newData.sensitive_strategy === '其他') {
          setShowSensitiveStrategyDesc(true);
        }
        form.setFieldsValue(newData);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id]);
  useEffect(() => {
    getOrgs().then((res: ResponseData<OrgsData[]>) => {
      const options = res.data.map((item) => ({ label: item.name, value: item.id }));
      options.push({ label: '其他数据来源', value: 'other' });
      setOwnerDeptOptions(options);
    });
  }, []);
  useImperativeHandle(ref, () => {
    return {
      validate: async () => {
        return await form.validateFields();
      },
    };
  });
  const handleValuesChange = (changedValues: any, allValues: any) => {
    const changedField = Object.keys(changedValues)[0];
    if (changedField === 'pass') {
      setShowAdvice(!allValues.pass);
    }
  };
  return (
    <Spin spinning={loading}>
      <ApplyForm
        labelWidth="180px"
        labelAlign="right"
        itemGutter={12}
        itemSpan={12}
        size="small"
        form={form}
        onValuesChange={handleValuesChange}
      >
        <SubTitle title="申请人信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Applicant
            disabled
            rules={{
              applicant_company: [
                {
                  required: true,
                },
              ],
              applicant_dept: [
                {
                  required: true,
                },
              ],
              applicant_name: [
                {
                  required: true,
                },
              ],
              applicant_roles_str: [
                {
                  required: true,
                },
              ],
              applicant_mobile: [
                {
                  required: true,
                },
              ],
              applicant_date: [
                {
                  required: true,
                },
              ],
            }}
          />
        </div>
        <SubTitle title="基础信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Base
            ownerDeptOptions={ownerDeptOptions}
            showOwnerDeptDesc={showOwnerDeptDesc}
            disabled
            rules={{
              num: [
                {
                  required: true,
                },
              ],
              name: [
                {
                  required: true,
                },
              ],
              owner_dept_id: [
                {
                  required: true,
                },
              ],
              owner_dept_detail: [
                {
                  required: true,
                },
              ],
              source_channel: [
                {
                  required: true,
                },
              ],
              source_name: [
                {
                  required: true,
                },
              ],
            }}
          />
        </div>
        <SubTitle title="业务信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Business
            disabled
            rules={{
              first_biz_domain: [
                {
                  required: true,
                },
              ],
              second_biz_domain: [
                {
                  required: true,
                },
              ],
              third_biz_domain: [
                {
                  required: true,
                },
              ],
            }}
          />
        </div>
        <SubTitle title="技术信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Tech
            disabled
            approvalView
            form={form}
            showSensitiveStrategyDesc={showSensitiveStrategyDesc}
            rules={{
              main_info: [
                {
                  required: true,
                },
              ],
              data_fields: [
                {
                  required: true,
                },
              ],
              usability_desc: [
                {
                  required: true,
                },
              ],
              security_level: [
                {
                  required: true,
                },
              ],
              share_type: [
                {
                  required: true,
                },
              ],
              sensitive_desc: [
                {
                  required: true,
                },
              ],
              sensitive_strategy: [
                {
                  required: true,
                },
              ],
              senstive_strategy_desc: [
                {
                  required: true,
                },
              ],
              update_frequency: [
                {
                  required: true,
                },
              ],
              biz_scale: [
                {
                  required: true,
                },
              ],
              db_type: [
                {
                  required: true,
                },
              ],
              db_name: [
                {
                  required: true,
                },
              ],
              table_name: [
                {
                  required: true,
                },
              ],
              storage_type: [
                {
                  required: true,
                },
              ],
            }}
          />
        </div>
        <SubTitle title="审批意见" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.ApprovalOpinion
            currentNode={form.getFieldValue('current_node')}
            showAdvice={showAdvice}
            rules={{
              pass: [
                {
                  required: true,
                  message: '请选择审核意见',
                },
              ],
              advice: [
                {
                  required: true,
                  message: '请输入审批意见',
                },
              ],
            }}
          />
        </div>
      </ApplyForm>
    </Spin>
  );
});
