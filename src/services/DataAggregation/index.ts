import { getToken } from '@/utils/loginUntils';
import { request } from 'umi';

// 获取数据聚合列表
export const GetDataAggregation = async (params: any) => {
  return request('/api/v1/dataset/main-page', {
    method: 'GET',
    params,
    headers: {
      authorization: getToken() || '',
    },
  });
};

// 获取采集状况
export const GetCollectionStatus = async (params: any) => {
  return request('/api/v1/dataset/secondary-page', {
    method: 'GET',
    params,
    headers: {
      authorization: getToken() || '',
    },
  });
};

// 获取采集信息
export const GetCollectionInfo = async (params: any) => {
  return request('/api/v1/dataset/third-page', {
    method: 'GET',
    params,
    headers: {
      authorization: getToken() || '',
    },
  });
};

// 获取采集详情
export const GetCollectionDetail = async (params: any) => {
  return request('/api/v1/dataset/third-page-detail', {
    method: 'GET',
    params,
    headers: {
      authorization: getToken() || '',
    },
  });
};
