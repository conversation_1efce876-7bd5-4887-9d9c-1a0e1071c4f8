import { ReactComponent as ApplyIcon } from '@/assets/report_icon.svg';
import { ConfigProvider, Segmented } from 'antd';
import React from 'react';
import DataResources from './dataResources';
import MasterData from './masterData';

type View = '数据资源申请管理' | '主数据申请管理';

function Apply() {
  const [viewValue, setViewValue] = React.useState<View>('主数据申请管理');
  return (
    <ConfigProvider
      theme={{
        components: {
          Segmented: {
            itemSelectedBg: '#002FA5',
            itemSelectedColor: '#fff',
            itemHoverColor: '#fff',
            itemHoverBg: 'rgba(0, 47, 165, 0.15)',
          },
          List: {
            metaMarginBottom: '0px',
          },
        },
      }}
    >
      <div>
        <h1 className="flex gap-x-4 items-center justify-center">
          <ApplyIcon height="40px" width="40px" />
          <span className="text-3xl font-medium">使用申请管理</span>
        </h1>
        <div className="mt-5">
          <div className="my-5 flex items-center">
            <div className="space-x-2 flex-1">
              <Segmented
                size="large"
                style={{ border: '1px solid #D9D9D9' }}
                defaultValue="主数据申请管理"
                onChange={(value) => setViewValue(value as View)}
                value={viewValue}
                options={['主数据申请管理', '数据资源申请管理']}
              />
            </div>
          </div>
        </div>
        {viewValue === '数据资源申请管理' && <DataResources />}
        {viewValue === '主数据申请管理' && <MasterData />}
      </div>
    </ConfigProvider>
  );
}
export default Apply;
