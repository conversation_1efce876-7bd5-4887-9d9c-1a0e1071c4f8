name: deploy
on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'warning'
      tags:
        description: 'Test scenario tags'
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: 20

      - run: yarn install
      - run: NODE_OPTIONS=--openssl-legacy-provider yarn build

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: unnecessary

      - name: Adding Known Hosts
        run: ssh-keyscan -p ${{ secrets.SSH_PORT}} -H dev-new.cel-la.store  >> ~/.ssh/known_hosts

      - name: Deploy
        run: sh manual.sh poly-bigdata-cms ubuntu dev-new.cel-la.store "sudo docker-compose build" "sudo docker-compose stop" "sudo docker-compose up -d" "sudo docker ps -a"
