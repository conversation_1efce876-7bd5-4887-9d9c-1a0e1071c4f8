// 接口
import { getToken } from '@/utils/loginUntils';
import { request } from 'umi';

export async function LoginPost(data: { username: string; password: string }) {
  return request('/api/v1/login', {
    method: 'POST',
    data: data,
  });
}

/**
 * UC 单点登录
 * @param data
 * @returns
 */
export async function UcLogin(data: { code: string }) {
  return request('/api/v1/uc-login', {
    method: 'POST',
    data: data,
  });
}

export async function GetUserInfo() {
  return request('/api/v1/user-info', {
    method: 'GET',
    headers: { authorization: getToken() ?? '' },
  });
}

export async function RegisterPost(data: any) {
  return request('/api/v1/register', {
    method: 'POST',
    data,
  });
}

export async function GetVerificationCode(mobile: string) {
  return request('/api/v1/update-pwd-sms', {
    method: 'POST',
    data: { mobile },
  });
}

export async function ModifyPassword(data: any) {
  return request('/api/v1/update-pwd', {
    method: 'PUT',
    data,
  });
}
