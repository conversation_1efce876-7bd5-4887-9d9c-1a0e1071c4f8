import { ReactComponent as CompanyIcon } from '@/assets/center_icon.svg';
import { ReactComponent as SafeIcon } from '@/assets/safe_icon.svg';
import { ReactComponent as SensitiveIcon } from '@/assets/sensitive_icon.svg';
import { ReactComponent as ShareIcon } from '@/assets/share_icon.svg';
import { history } from '@umijs/max';
import { List, Space, Tooltip } from 'antd';
import { Application } from './aplication';
import styles from './style.less';
interface listCardProps {
  data: any[];
  setCurrentPage: (val: any) => void;
  setPageSize: (val: any) => void;
  pageSize: number;
  totalNum: number;
  currentPage: number;
}

export function ListCard({
  data = [],
  setCurrentPage,
  pageSize,
  setPageSize,
  totalNum,
  currentPage,
}: listCardProps) {
  const IconText = ({ icon, text }: { icon: any; text: string }) => (
    <Space>
      {icon}
      <span className="text-[#333]">{text}</span>
    </Space>
  );
  return (
    <List
      itemLayout="vertical"
      size="large"
      pagination={{
        total: totalNum,
        onChange: (page) => {
          setCurrentPage(page);
        },
        onShowSizeChange: (current, pageSize) => {
          setPageSize(pageSize);
        },
        pageSize,
        current: currentPage,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => (
          <>
            共 {totalNum} 条 本页显示第 {range[0]} ~ {range[1]} 条
          </>
        ),
      }}
      dataSource={data}
      footer={<></>}
      className={styles['list']}
      renderItem={(item) => (
        <List.Item
          key={item.title}
          className="border rounded-md mt-5 w-full overflow-hidden flex"
          actions={[
            <IconText icon={<CompanyIcon />} text={item.owner_dept} key="first_biz_domain+" />,
            <IconText icon={<ShareIcon />} text={item.share_type} key="list-vertical-like-o" />,
            <IconText
              icon={<SafeIcon />}
              text={`安全等级: ${item.security_level}`}
              key="list-vertical-like-1"
            />,
            <IconText
              icon={<SensitiveIcon />}
              text={item.sensitive_strategy}
              key="list-vertical-message"
            />,
          ]}
          extra={
            <div className="flex flex-col items-end justify-between h-full">
              <div className="">
                <span className="text-[#FF0000] ">{item.amount}</span>
                <span className="text-[#999] ml-[4px]">条数据</span>
              </div>
              <div className="py-[16px] flex-1">
                <span className="text-[#999]">{item?.source_channel} - </span>
                {item?.source_name?.length > 10 ? (
                  <Tooltip title={item?.source_name}>
                    <span className="text-[#FF0000] cursor-pointer">
                      {item?.source_name?.substring(0, 10) + '...'}
                    </span>
                  </Tooltip>
                ) : (
                  <span className="text-[#FF0000]">{item?.source_name}</span>
                )}
              </div>
              <div className="text-[#002FA5] flex gap-[20px] text-[14px] font-normal leading-[19.6px]">
                {item?.operations?.can_view && (
                  <a
                    className="hover:underline"
                    onClick={() => history.push(`/dataResources/list/detail/${item?.id}`)}
                  >
                    查看详情
                  </a>
                )}
                {item?.operations?.can_apply_use && <Application type="list" id={item?.id} />}
                {item?.operations?.can_update && (
                  <a
                    className="hover:underline"
                    onClick={() => history.push(`/dataResources/report/apply?id=${item?.id}`)}
                  >
                    编辑更改
                  </a>
                )}
              </div>
            </div>
          }
        >
          <List.Item.Meta
            title={
              <div className="font-normal text-[#333]">
                <span className="text-[16px] leading-[22.4px]">
                  <span className="text-[#002FA5] text-[18px] font-medium">资源名称：</span>
                  {item?.name?.length > 15 ? (
                    <Tooltip title={item?.name}>{item?.name?.substring(0, 15) + '...'}</Tooltip>
                  ) : (
                    item?.name
                  )}
                </span>
                <span className="text-[12px] leading-[16.8px] ml-[12px] ">
                  负责人 : {item.head_name}
                </span>
                <span className="text-[12px] leading-[16.8px] ml-[12px] text-[#999]">
                  {item.num}
                </span>
              </div>
            }
            description={item.description}
          />
          <div className="font-medium text-[#002FA5] text-[18px] leading-[25.2px] mb-[16px] truncate hover:overflow-auto hover:whitespace-normal">
            主要数据信息:
            {(item?.data_fields || item?.main_info || '')
              ?.split('、')
              ?.map((subItem: string, index: number) => {
                return (
                  <>
                    <span
                      key={index}
                      className={`font-normal text-base ${index === 0 ? 'ml-[10px]' : ''}`}
                    >
                      {subItem}
                    </span>
                    {item?.main_info?.split('、')?.length - 1 === index ? null : (
                      <span className="mx-[10px] w-[2px] bg-[#002FA5] h-[12px] inline-block"></span>
                    )}
                  </>
                );
              })}
          </div>
          <div className="flex gap-3 flex-wrap">
            <div
              className={`p-1 flex items-center gap-2 rounded-sm ${'bg-[#002fa50d] text-[#002fa5]'}`}
            >
              {item?.first_biz_domain}
            </div>
            <div
              className={`p-1 flex items-center gap-2 rounded-sm ${'bg-[#002fa50d] text-[#002fa5]'}`}
            >
              {item?.second_biz_domain}
            </div>
            <div
              className={`p-1 flex items-center gap-2 rounded-sm ${'bg-[#002fa50d] text-[#002fa5]'}`}
            >
              {item?.third_biz_domain}
            </div>
            <div
              className={`p-1 flex items-center gap-2 rounded-sm ${'bg-[#00BAA412] text-[#00BAA4]'}`}
            >
              {item?.biz_scale}
            </div>
            <div
              className={`p-1 flex items-center gap-2 rounded-sm ${'bg-[#B3560012] text-[#B35600]'}`}
            >
              {item?.update_frequency}
            </div>
          </div>
        </List.Item>
      )}
    />
  );
}
