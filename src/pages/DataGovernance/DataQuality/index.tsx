import React, { useEffect, useState } from 'react';
import './index.less';

const DataQuality: React.FC = () => {
  const [height, setHeight] = useState<any>(0);
  useEffect(() => {
    setHeight(window.innerHeight - 50);
  }, []);

  return (
    <div className="reform_portrait_page">
      <iframe
        width="100%"
        height={height}
        id="iframeId"
        src="/index/quality/quality"
        frameBorder={0}
        allowFullScreen={true}
      ></iframe>
    </div>
  );
};

export default DataQuality;
