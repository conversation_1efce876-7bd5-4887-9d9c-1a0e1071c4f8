import { getToken } from '@/utils/loginUntils';
import { request } from 'umi';

export async function GetAccountList() {
  return request('/api/v1/users?current=1&pageSize=99', {
    method: 'GET',
    headers: { authorization: getToken() ?? '' },
  });
}

export async function DeleteAccountItem(ids: string[]) {
  return request('/api/v1/users', {
    method: 'DELETE',
    headers: { authorization: getToken() ?? '' },
    data: { ids },
  });
}

export async function EditAccountItem(data: any) {
  return request('/api/v1/users', {
    method: 'PUT',
    headers: { authorization: getToken() ?? '' },
    data,
  });
}

export async function AddAccountItem(data: any) {
  return request('/api/v1/users', {
    method: 'POST',
    headers: { authorization: getToken() ?? '' },
    data,
  });
}

export async function GetRegisterList(type: string) {
  return request(`/api/v1/users/register?current=1&pageSize=99&status=${type}`, {
    method: 'GET',
    headers: { authorization: getToken() ?? '' },
  });
}

export async function AuditRegisterItem({ id, pass }: { id: string; pass: boolean }) {
  return request('/api/v1/users/register/approve', {
    method: 'PUT',
    headers: { authorization: getToken() ?? '' },
    data: { id, pass },
  });
}
