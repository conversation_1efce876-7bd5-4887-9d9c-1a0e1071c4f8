import { PostMajorDataApply } from '@/services/dataResources/listApi';
import { getOrgsTree } from '@/services/dataResources/orgsApi';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Cascader, Col, Form, Input, message, Modal, Radio, Row, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { history } from 'umi';

export const MajorDataApplication = ({
  majorDataId,
  majorDataName,
  approvalType,
  isModalOpen,
  setIsModalOpen,
}: {
  majorDataId: string;
  majorDataName: string;
  approvalType: number;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [form] = Form.useForm();

  const [agree, setAgree] = useState<any>(false);
  const [readModalOpen, setReadModalOpen] = useState(false);

  const [loading, setLoading] = useState(false);

  const [ORGS, setORGS] = useState<any[]>([]);
  const [orgCascaderOptions, setOrgCascaderOptions] = useState<any[]>([]);

  const flattenOrgs = (orgs: any[]) => {
    const result: any[] = [];
    const recurse = (nodes: any[]) => {
      nodes.forEach((node) => {
        result.push(node);
        if (node.children) {
          recurse(node.children);
        }
      });
    };
    recurse(orgs);
    return result;
  };
  const convertOrgsToCascaderOptions = (orgs: any[], depth = 1): any[] =>
    orgs.map((org) => ({
      value: org.id,
      label: org.name,
      children:
        org.children && depth < 3
          ? convertOrgsToCascaderOptions(org.children, depth + 1)
          : undefined,
    }));

  const deleteQueryParams = () => {
    const url = new URL(window.location.href);
    url.search = '';
    history.replace(url.pathname);
  };

  const getOrgs = async () => {
    setLoading(true);
    try {
      const res = await getOrgsTree();

      if (res.code === 200001) {
        const orgsData = convertOrgsToCascaderOptions(res.data);

        setORGS(flattenOrgs(res.data).map((org) => ({ label: org.name, value: org.id })));
        setOrgCascaderOptions(orgsData);
        return;
      }

      message.error(res.message);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  const handleOk = async () => {
    setLoading(true);

    const values = form.getFieldsValue();

    values.collect_frequency = 'default';
    values.collect_period = 'default';
    values.store_location = 'default';
    values.macs = values.macs.split('\n');

    values.majorData_id = majorDataId;

    if (approvalType === 2 || majorDataName === '客户') {
      values.approval_company_id =
        values.approval_company_id?.[values.approval_company_id.length - 1];
    }

    try {
      const res = await PostMajorDataApply(majorDataId, values);
      if (res.code === 200001) {
        message.success('操作成功');
        setIsModalOpen(false);
        deleteQueryParams();
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  const handleOkRead = async () => {
    form.setFieldValue('is_agree', true);
    setAgree(true);
    setReadModalOpen(false);
  };
  const handleCancelRead = () => {
    form.setFieldValue('is_agree', false);
    setAgree(false);
    setReadModalOpen(false);
  };

  useEffect(() => {
    getOrgs();
  }, []);

  const IP_REGEX =
    /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

  const validateIPs = (_, value) => {
    const lines = value
      .split('\n')
      .map((ip) => ip.trim())
      .filter((ip) => ip);
    if (lines.length === 0) return Promise.reject('至少输入一个 IP 地址');

    const invalidIPs = lines.filter((ip) => !IP_REGEX.test(ip));
    if (invalidIPs.length > 0) {
      return Promise.reject(`以下 IP 格式不正确：${invalidIPs.join(', ')}`);
    }
    return Promise.resolve();
  };

  return (
    <>
      <Modal
        title={majorDataName + '主数据使用申请'}
        open={isModalOpen}
        width={800}
        styles={{ mask: { backgroundColor: 'rgba(0, 0, 0, 0.25)' } }}
        footer={null}
      >
        <Spin spinning={loading}>
          <Form
            form={form}
            labelCol={{ span: 8 }}
            labelAlign="right"
            onFinish={handleOk}
            onReset={() => {
              form.setFieldsValue({});
              setIsModalOpen(false);
              deleteQueryParams();
            }}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="需求描述"
                  name="req_desc"
                  rules={[{ required: true, message: '需求描述为必填' }]}
                >
                  <Input.TextArea
                    style={{ height: '80px' }}
                    autoSize={{ minRows: 2, maxRows: 3 }}
                  />
                </Form.Item>
              </Col>
            </Row>

            {(approvalType === 2 || majorDataName === '客户') && (
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    label="数据所属组织架构"
                    name="approval_company_id"
                    rules={[{ required: true, message: '数据所属组织架构为必填' }]}
                  >
                    <Cascader
                      options={orgCascaderOptions}
                      changeOnSelect
                      showSearch
                      onSearch={(value) => {
                        if (value) {
                          const filteredOptions = ORGS.filter((option) =>
                            option.label.includes(value),
                          );
                          setOrgCascaderOptions(filteredOptions);
                        } else {
                          setOrgCascaderOptions(ORGS);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="数据使用合规性声明"
                  tooltip={{
                    title: '请仔细阅读数据使用合规性声明',
                    icon: <InfoCircleOutlined />,
                  }}
                  wrapperCol={{ offset: 1 }}
                  name="is_agree"
                  style={{ marginBottom: '0px' }}
                  rules={[{ required: true, message: '是否同意数据使用合规性声明为必填' }]}
                >
                  <Radio.Group
                    onChange={(e) => {
                      if (e.target.value) {
                        setReadModalOpen(true);
                      }
                      setAgree(e.target.value);
                    }}
                  >
                    <Radio value={true}>同意</Radio>
                    <Radio value={false}>不同意</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8} className="text-right">
                <a className="text-[#2F54EB]" onClick={() => setReadModalOpen(true)}>
                  再次阅读
                </a>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="数据安全措施"
                  name="security_step"
                  rules={[{ required: true, message: '数据安全措施为必填' }]}
                  className="w-full"
                >
                  <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} showCount maxLength={100} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="访问源硬件地址"
                  name="macs"
                  rules={[{ required: true, message: '访问源硬件地址为必填' }]}
                  className="w-full"
                >
                  <Input.TextArea
                    autoSize={{ minRows: 3, maxRows: 5 }}
                    placeholder="请输入访问源硬件地址，每行为一个地址，多个地址用 Enter 分隔"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="访问源 ip 地址"
                  name="limit_ips"
                  rules={[
                    { required: true, message: '访问源 ip 地址为必填' },
                    { validator: validateIPs },
                  ]}
                  className="w-full"
                >
                  <Input.TextArea
                    autoSize={{ minRows: 3, maxRows: 5 }}
                    placeholder="请输入访问源 ip 地址，每行为一个地址，多个地址用 Enter 分隔"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="备注" name="remark" className="w-full">
                  <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={24} className="flex gap-2 justify-end">
                <Button type="primary" htmlType="submit" disabled={!agree}>
                  提交申请
                </Button>
                <Button htmlType="reset">取消</Button>
              </Col>
            </Row>
          </Form>
        </Spin>
      </Modal>

      <Modal
        maskClosable={false}
        open={readModalOpen}
        cancelButtonProps={{
          onClick: handleCancelRead,
        }}
        onOk={handleOkRead}
        width={600}
        closable={false}
        okText="阅读并同意"
        cancelText="不同意"
        styles={{ mask: { backgroundColor: 'rgba(0, 0, 0, 0.25)' } }}
      >
        <div className="font-semibold text-[18px] text-center pb-[20px]">
          数据使用安全合规声明承诺书
        </div>
        <div className="h-[300px] overflow-y-scroll">
          <h1 className="font-medium text-[15px] ">1. 数据获取和使用保证</h1>
          <p className="pl-[20px]">
            合法获取：数据使用方应按照规定的申请流程提交数据使用申请，提供真实、准确、完整的申请材料，确保获得数据提供方的明确授权，接受数据提供方的安全能力评估。
          </p>
          <p className="pl-[20px]">
            目的限制：需明确说明数据使用的目的、范围和方式，严格按照承诺的目的使用数据，不得扩大或修改使用范围或改变用途，不得非法出售或者非法向他人提供数据，不泄露、篡改、毁损所收集的数据。
          </p>
          <p className="pl-[20px]">
            最小必要：遵循最小必要原则，仅收集和使用完成已审批目的所需的最少数据。
          </p>
          <h1 className="font-medium text-[15px] ">2. 数据存储和管理保证</h1>
          <p className="pl-[20px]">
            安全存储：采用安全可靠的存储系统。确保法律要求本地化存储的数据在我国境内存储，不得向境外传输。对于敏感个人信息，应采用更高强度的加密手段，并确保数据仅存储于具备合规认证的服务器上。
          </p>
          <p className="pl-[20px]">
            安全管理：应指定数据安全保密的责任人，限定直接接触、使用、查阅、管理数据的人员，并承诺以上人员进行定期数据安全及保密教育，需签订保密责任书。
          </p>
          <h1 className="font-medium text-[15px] ">3. 数据访问和控制保证</h1>
          <p className="pl-[20px]">
            访问控制：确保只有经过授权的人员可以访问和处理数据，并建立访问日志，便于追溯和审计。
          </p>
          <p className="pl-[20px]">
            隐私保护：承诺严格遵守相关法律法规，对个人敏感数据和重要信息进行加密，保障安全性和保密性。
          </p>
          <p className="pl-[20px]">
            行为监控：建立数据安全监管体系，监测和记录数据使用行为，及时发现和处理异常情况。
          </p>
          <h1 className="font-medium text-[15px] ">4. 数据传输和共享保证</h1>
          <p className="pl-[20px]">
            安全传输：在对数据进行传输时需根据分类分级的要求确保传输过程中数据的保密性和完整性，必要时应采用加密、脱敏等数据安全措施。
          </p>
          <p className="pl-[20px]">
            授权共享：未经数据提供方明确授权，不得向第三方共享或转让数据，任何与第三方的数据共享活动均需遵守本承诺书及相关的数据保护法规要求。
          </p>
          <p className="pl-[20px]">
            跨境传输：如需进行数据跨境共享传输，须获得数据提供方及具体数据主体的书面同意并获得必要的授权和许可，并遵守相关合规义务。
          </p>
          <h1 className="font-medium text-[15px] ">5. 数据删除和销毁保证</h1>
          <p className="pl-[20px]">
            及时销毁：数据使用期限届满或约定目的实现后，应及时、彻底地销毁相关数据。
          </p>
          <p className="pl-[20px]">
            销毁方式：数据销毁应采用不可恢复的方式，确保数据无法被恢复或重建。
          </p>
          <p className="pl-[20px]">
            销毁证明：保留数据销毁的证明文件，并在必要时向数据提供方提供。
          </p>
          <h1 className="font-medium text-[15px] ">6. 合规监督和管理保证</h1>
          <p className="pl-[20px]">
            内部合规：制定全面的数据安全合规政策，明确各部门和岗位的职责。定期开展数据安全合规培训，提高全员数据安全合规意识和能力。
          </p>
          <p className="pl-[20px]">
            外部审计：承诺接受第三方机构进行数据安全合规审计。审计结果应形成正式报告，汇报主管部门，共享给提供方。
          </p>
          <p className="pl-[20px]">
            持续改进：建立问题跟踪机制，确保审计中发现的问题得到及时整改，并根据评估结果进行调整和优化合规措施。
          </p>
          <h1 className="font-medium text-[15px] ">7. 安全应急和响应保证</h1>
          <p className="pl-[20px]">
            事件报告：建立内部安全事件报告机制，确保及时发现和向上汇报给主管部门以及数据提供方。
          </p>
          <p className="pl-[20px]">
            应急处理：制定详细的安全事件应急预案，明确各方职责和处理流程。采取必要措施控制损失扩大。在处理安全事件过程中注意保全相关证据，以备后续调查和分析。
          </p>
          <p className="pl-[20px]">
            影响评估：对安全事件造成的影响进行全面评估，包括数据泄露范围、受影响的数据主体数量、可能造成的损害等。深入分析安全事件的根本原因，识别系统、流程或人员方面的漏洞。基于评估和分析结果，提出具体的持续改进措施。
          </p>
          <h1 className="font-medium text-[15px] ">8. 环境安全和培训保证</h1>
          <p className="pl-[20px]">
            安全修复：定期进行系统和应用程序的漏洞扫描和修复，确保网络和系统的安全性；
          </p>
          <p className="pl-[20px]">
            安全培训：建立合理的培训机制，提高员工数据保护意识和技能，加强数据安全管理。
          </p>
          <p className="pl-[20px]">
            安全监督：根据需要配备数据安全管理人员，统筹负责数据处理活动的安全管控和监督管理。
          </p>
          <h1 className="font-medium text-[15px] ">9. 数据服务商管理保证</h1>
          <p className="pl-[20px]">
            如果数据使用方涉及外包第三方服务商处理数据，应评估并确保该服务商具备相应的数据安全管理能力，并与其签订数据安全协议，明确数据安全责任和义务，并定期对外包服务商进行安全合规审查，明确审查的频率和评价标准。
          </p>
          <p className="pl-[20px]">
            数据服务商需严格按照本承诺书对数据使用方的数据安全合规使用承诺的约定处理数据，确保数据处理安全。对参与数据处理的相关人员进行安全培训，并签署保密协议。
          </p>
        </div>
      </Modal>
    </>
  );
};
