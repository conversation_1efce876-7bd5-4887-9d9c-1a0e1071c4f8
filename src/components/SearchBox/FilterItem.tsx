import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown } from 'antd';
import cn from 'classnames';
import { isEmpty, isNil } from 'lodash';
import { ItemType } from 'rc-menu/lib/interface';
import { useEffect, useRef, useState } from 'react';
import { FilterOption, FiltersObj } from '.';

interface FilterItemProps {
  selected: FiltersObj;
  item: FilterOption;
  filterLabelSuffix?: string;
  filterLabelWidth?: string | number;
  handleAllClick: (item: FilterOption) => void;
  isSelected: (option: FilterOption, value: string | number) => boolean;
  updateSelected: (option: FilterOption, value: string | number) => void;
  className?: string;
}

export const FilterItem = ({
  selected,
  item,
  filterLabelWidth,
  filterLabelSuffix,
  handleAllClick,
  isSelected,
  updateSelected,
  className,
}: FilterItemProps) => {
  const [expand, setExpand] = useState(false);
  const [showExpand, setShowExpand] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // 判断是否需要展示“展开/收起”按钮的函数
  const checkOverflow = () => {
    if (contentRef.current) {
      const { scrollHeight, clientHeight } = contentRef.current;
      setShowExpand(scrollHeight > clientHeight);
    }
  };

  useEffect(() => {
    checkOverflow();
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver(() => {
      checkOverflow();
    });

    if (contentRef.current) {
      observer.observe(contentRef.current);
    }

    // 在组件卸载时清理 observer
    return () => {
      if (contentRef.current) {
        observer.unobserve(contentRef.current);
      }
    };
  }, [item.options]);

  const renderCascadingOptions = (options: FilterOption['options']) => {
    return options.map((option) => {
      if (option.children && option.children.length) {
        return (
          <Dropdown
            key={option.key}
            menu={{
              items: option.children as ItemType[],
              onClick: ({ key }) => updateSelected(item, key),
            }}
            trigger={['hover']}
          >
            <div
              className={cn('cursor-pointer p-1 rounded-sm', {
                'bg-[#002fa50d] text-[#002fa5]': isSelected(item, option.key!),
              })}
              onClick={() => updateSelected(item, option.key!)}
            >
              {option.label}
              <DownOutlined className="ml-1" />
            </div>
          </Dropdown>
        );
      }
      return (
        <div
          key={option.key}
          className={cn('cursor-pointer p-1 rounded-sm', {
            'bg-[#002fa50d] text-[#002fa5]': isSelected(item, option.key!),
          })}
          onClick={() => updateSelected(item, option.key!)}
        >
          {option.label}
        </div>
      );
    });
  };

  return (
    <div className={cn('flex gap-2', className)} key={item.key}>
      <div className="flex-shrink-0 flex-grow-0 p-1" style={{ width: filterLabelWidth }}>
        {item.label}
        {filterLabelSuffix}
      </div>
      <div
        className={cn('flex-1 flex gap-3 text-gray-500 flex-wrap overflow-hidden', {
          'max-h-7': !expand,
          'max-h-[none]': expand,
        })}
        ref={contentRef}
      >
        <div
          className={cn('cursor-pointer p-1 rounded-sm', {
            'bg-[#002fa50d] text-[#002fa5]':
              isNil(selected[item.key!]) || isEmpty(selected[item.key!]),
          })}
          onClick={() => handleAllClick(item)}
        >
          全部
        </div>

        {renderCascadingOptions(item.options)}
      </div>
      <div className="flex-shrink-0 flex-grow-0 ml-2 w-[100px]">
        {(showExpand || expand) && (
          <Button
            size="small"
            onClick={() => setExpand(!expand)}
            className="flex items-center justify-center"
          >
            {!expand ? '更多选项' : '收起更多'}
            <DownOutlined
              className={cn('transition-transform duration-500', {
                'rotate-180': expand,
              })}
            />
          </Button>
        )}
      </div>
    </div>
  );
};
