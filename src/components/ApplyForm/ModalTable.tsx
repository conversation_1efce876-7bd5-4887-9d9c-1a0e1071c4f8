import { ActionType, EditableProTable } from '@ant-design/pro-components';
import { Button, ConfigProvider, Form, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

export const ModalTable = ({ mainForm, value, approvalView }: any) => {
  const [visible, setVisible] = useState(false);
  const actionRef = useRef<ActionType>();
  //form
  const [form] = Form.useForm(); // 创建 Form 实例
  //page
  const [currentPage, setCurrentPage] = React.useState<any>(1);
  const [pageSize, setPageSize] = React.useState<number>(10);

  const columns = [
    {
      title: '序号',
      dataIndex: 'sequence',
      key: 'sequence',
      width: approvalView ? '12%' : '6%',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      renderFormItem: ({ index }: any, config: any, form: any) => <>{index + 1}</>,
    },
    {
      title: '字段名',
      dataIndex: 'field',
      key: 'field',
      editable: approvalView ? false : true,
      formItemProps: {
        rules: [{ required: true, message: '请输入字段名' }],
      },
    },
    {
      title: '中文名称',
      dataIndex: 'cn_name',
      key: 'cn_name',
      editable: approvalView ? false : true,
      formItemProps: {
        rules: [{ required: true, message: '请输入中文名称' }],
      },
    },
    {
      title: '字段描述',
      key: 'description',
      dataIndex: 'description',
      editable: approvalView ? false : true,
      formItemProps: {
        rules: [{ required: true, message: '请输入字段描述' }],
      },
    },
    // {
    //   title: '字段类型',
    //   key: 'data_type',
    //   dataIndex: 'data_type',
    // },
    {
      title: '敏感状态',
      valueType: 'select',
      key: 'is_sensitive',
      dataIndex: 'is_sensitive',
      editable: approvalView ? false : true,
      formItemProps: {
        rules: [{ required: true, message: '请选择敏感状态' }],
      },
      fieldProps: {
        options: [
          { label: '敏感', value: true },
          { label: '不敏感', value: false },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      hideInTable: approvalView,
      width: '10%',
      render: () => {
        return null;
      },
    },
  ];

  const [dataSource, setDataSource] = useState<any[]>(value);

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() =>
    dataSource?.map((item) => item.id),
  );

  useEffect(() => {
    setDataSource(value || []);
    setEditableRowKeys(value?.map((item: any) => item.id) || []);
  }, [value]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBg: '#F3F5FB',
          },
        },
      }}
    >
      <Button
        type="primary"
        onClick={() => {
          setVisible(true);
        }}
      >
        {approvalView ? '查看数据项' : '录入数据项'}
      </Button>
      <Modal
        title={approvalView ? '查看数据项' : '录入数据项'}
        open={visible}
        width="60vw"
        bodyStyle={{ minHeight: '60vh' }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={() => {
          setVisible(false);
        }}
        footer={() => (
          <div className="text-center">
            {approvalView ? (
              <></>
            ) : (
              <Button
                size="middle"
                className="mr-2"
                onClick={() => {
                  setVisible(false);
                  setDataSource(value);
                }}
              >
                取消
              </Button>
            )}
            <Button
              type="primary"
              size="middle"
              onClick={
                approvalView
                  ? () => {
                      setVisible(false);
                    }
                  : async () => {
                      try {
                        await form.validateFields();
                        setVisible(false);
                        mainForm?.setFieldValue('data_items', dataSource);
                      } catch (errorInfo) {
                        console.log('Validate Failed:', errorInfo);
                        message.error('当前有必填项未进行填写,请检查表单后再提交');
                      }
                    }
              }
            >
              确认
            </Button>
          </div>
        )}
      >
        <EditableProTable
          actionRef={actionRef}
          recordCreatorProps={
            approvalView
              ? false
              : {
                  newRecordType: 'dataSource',
                  record: () => ({
                    id: `新建${Date.now()}`,
                  }),
                  style: {
                    position: 'absolute',
                    top: 0,
                    right: '24px',
                    width: 'auto',
                  },
                  type: 'primary',
                  icon: <></>,
                  size: 'middle',
                  creatorButtonText: '添加字段',
                }
          }
          columns={columns}
          pagination={{
            total: dataSource?.length,
            onChange: (page) => {
              setCurrentPage(page);
            },
            onShowSizeChange: (current, pageSize) => {
              setPageSize(pageSize);
            },
            pageSize,
            current: currentPage,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => (
              <>
                共 {dataSource?.length} 条 本页显示第 {range[0]} ~ {range[1]} 条
              </>
            ),
          }}
          rowKey="id"
          scroll={{
            x: '54vw',
          }}
          value={dataSource}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys,
            deleteText: <span className="text-[red]">删除</span>,
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.delete];
            },
            onValuesChange: (record, recordList) => {
              setDataSource(recordList);
            },
            onChange: setEditableRowKeys,
          }}
          toolBarRender={() => {
            return [<div key="zhanwei" className="my-[15px]"></div>];
          }}
        />
      </Modal>
    </ConfigProvider>
  );
};
