#!/bin/sh

name=$1
user=$2
ip=$3
command1=$4
command2=$5
command3=$6

rsync -ahqzt --delete --exclude .git --exclude "node_modules" . $user@$ip:/home/<USER>/$name 

command="cd /home/<USER>/$name && pwd"
if [ -n "$command1" ]
then
  command="$command && $command1"
fi
if [ -n "$command2" ]
then
  command="$command && $command2"
fi
if [ -n "$command3" ]
then
  command="$command && $command3"
fi

ssh $user@$ip "$command"