import { getOrgsTree } from '@/services/dataResources/orgsApi';
import { RegisterPost } from '@/services/Login';
import { Helmet, history, useIntl } from '@umijs/max';
import { Button, Col, Form, Input, Row, Select, message } from 'antd';
import { useEffect, useState } from 'react';
import Settings from '../../../../config/defaultSettings';

interface IOrg {
  id: string;
  name: string;
}

interface IRegisterForm {
  full_name: string;
  company_id: string;
  email: string;
  mobile: string;
  password: string;
  _password: string;
  roles: string;
}

export default function Register() {
  const intl = useIntl();
  const [form] = Form.useForm<IRegisterForm>();
  const [loading, setLoading] = useState(false);
  const [orgs, setOrgs] = useState<IOrg[]>([]);

  const formItems = [
    {
      row: 0,
      items: [
        {
          name: 'full_name',
          label: '姓名',
          rules: [{ required: true, message: '请输入姓名' }],
          component: <Input placeholder="请输入姓名" type="text" />,
        },
        {
          name: 'company_id',
          label: '归口公司',
          rules: [{ required: true, message: '请输入归口公司' }],
          component: (orgs: IOrg[]) => (
            <Select placeholder="请选择归口公司">
              {orgs.map((org) => (
                <Select.Option key={org.id} value={org.id}>
                  {org.name}
                </Select.Option>
              ))}
            </Select>
          ),
        },
      ],
    },
    {
      row: 1,
      items: [
        {
          name: 'email',
          label: '邮箱',
          rules: [{ required: true, message: '请输入邮箱' }],
          component: <Input placeholder="请输入邮箱" type="email" />,
        },
        {
          name: 'mobile',
          label: '手机号',
          rules: [
            { required: true, message: '请输入手机号' },
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入有效的手机号',
            },
          ],
          component: <Input placeholder="请输入手机号" type="tel" />,
        },
      ],
    },
    {
      row: 2,
      items: [
        {
          name: 'username',
          label: '用户名',
          rules: [{ required: true, message: '请输入用户名' }],
          component: <Input placeholder="请输入用户名" type="text" />,
        },
        {
          name: 'roles',
          label: '角色',
          rules: [{ required: false, message: '请选择角色' }],
          component: () => (
            <Select
              options={[
                { label: '业务员', value: 'user' },
                { label: '普通用户', value: 'reporter' },
              ]}
              placeholder="请选择角色"
            />
          ),
        },
      ],
    },

    {
      row: 3,
      items: [
        {
          name: 'password',
          label: '密码',
          rules: [{ required: true, message: '请输入密码' }],
          component: <Input placeholder="请输入密码" type="password" />,
        },
        {
          name: '_password',
          label: '再次输入密码',
          rules: [
            { required: true, message: '请再次输入密码' },
            () => ({
              validator(_: any, value: string) {
                if (value !== form.getFieldValue('password')) {
                  return Promise.reject(new Error('两次输入的密码不一致'));
                }
                return Promise.resolve();
              },
            }),
          ],
          component: <Input placeholder="请再次输入密码" type="password" />,
        },
      ],
    },
  ];

  useEffect(() => {
    const fetchOrgs = async () => {
      try {
        const res = await getOrgsTree();
        if (res?.code === 200001) {
          setOrgs(
            res.data.filter(
              (item: any) =>
                item.parent_id === '' || item.parent_id === 'bb8d6171-bdc6-468d-b954-098d794fb6bd',
            ),
          );
        } else {
          message.error(res.message);
        }
      } catch (error) {
        message.error(JSON.stringify(error));
      }
    };
    fetchOrgs();
  }, []);

  const handleSubmit = async (values: IRegisterForm) => {
    try {
      setLoading(true);
      if (values.password !== values._password) {
        throw new Error('两次输入的密码不一致');
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { _password, roles, ...params } = values;
      const res = await RegisterPost({ ...params, roles: [roles] });

      if (res.code === 200001) {
        message.success('注册成功');
        history.push('/user/login');
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>
          {intl.formatMessage({ id: 'menu.register', defaultMessage: '注册页' })} - {Settings.title}
        </title>
      </Helmet>

      <div className="bg-[#002FA50D] h-screen w-screen flex">
        <div className="w-[845px] h-[612px] m-auto bg-white p-10 flex flex-col justify-between">
          <div className="text-[25px] font-bold text-center leading-[44px]">注册账号</div>

          <Form
            form={form}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            size="large"
            onFinish={handleSubmit}
          >
            {formItems.map((rowItem) => (
              <Row key={rowItem.row}>
                {rowItem.items.map((item) => (
                  <Col span={12} key={item.name}>
                    <Form.Item label={item.label} name={item.name} rules={item.rules}>
                      {typeof item.component === 'function' ? item.component(orgs) : item.component}
                    </Form.Item>
                  </Col>
                ))}
              </Row>
            ))}
          </Form>

          <div className="flex justify-center gap-12">
            <Button onClick={() => history.back()}>取消</Button>
            <Button type="primary" onClick={() => form.submit()} loading={loading}>
              提交申请
            </Button>
          </div>

          <div className="text-sm text-[#000000B2] text-center">
            * 提交申请后请联系公司管理员进行审核
          </div>
        </div>
      </div>
    </>
  );
}
