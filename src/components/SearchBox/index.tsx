import { getOrgsTree, OrgsTreeData } from '@/services/dataResources/orgsApi';
import { CloseOutlined, DownOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Select } from 'antd';
import cn from 'classnames';
import { isEmpty, isEqual, isNil } from 'lodash';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { FilterItem } from './FilterItem';
import styles from './index.module.less';

const FILTER_OPTIONS: FilterOption[] = [
  {
    label: '归口部门/单位',
    key: 'owner_dept_id',
    options: [],
    fetchOptions: async () => {
      const toOwnerData = (data: OrgsTreeData[]): FilterOptionItem[] => {
        return data.map((item) => ({
          label: item.name,
          key: item.id,
          children: item.children?.length
            ? [{ label: item.name, key: item.id }, ...toOwnerData(item.children)]
            : undefined,
        }));
      };
      let options: FilterOptionItem[] = [];
      try {
        const response = await getOrgsTree(true);
        if (response.code === 200001) {
          const data: OrgsTreeData[] = response.data || [];
          options = toOwnerData(data);
        }
      } catch (error) {}
      return options;
    },
    // options: [
    //   { label: '中国保利集团有限公司', key: '中国保利集团有限公司' },
    //   { label: '保利国际控股有限公司', key: '保利国际控股有限公司' },
    //   { label: '保利发展控股集团股份有限公司', key: '保利发展控股集团股份有限公司' },
    //   { label: '广东省重工建筑设计院', key: '广东省重工建筑设计院' },
    //   { label: '保利置业集团有限公司', key: '保利置业集团有限公司' },
    //   { label: '中国轻工集团有限公司', key: '中国轻工集团有限公司' },
    //   { label: '中国制浆造纸研究院有限公司', key: '中国制浆造纸研究院有限公司' },
    //   { label: '中国食品发酵工业研究院有限公司', key: '中国食品发酵工业研究院有限公司' },
    //   { label: '中国日用化学研究院有限公司', key: '中国日用化学研究院有限公司' },
    //   { label: '中国皮革制鞋研究院有限公司', key: '中国皮革制鞋研究院有限公司' },
    //   { label: '中轻日化科技有限公司', key: '中轻日化科技有限公司' },
    //   { label: '中轻检验认证有限公司', key: '中轻检验认证有限公司' },
    //   { label: '中国海诚工程科技股份有限公司', key: '中国海诚工程科技股份有限公司' },
    //   {
    //     label: '中轻长泰（长沙）智能科技股份有限公司',
    //     key: '中轻长泰（长沙）智能科技股份有限公司',
    //   },
    //   { label: '中国工艺集团有限公司', key: '中国工艺集团有限公司' },
    //   { label: '保利文化集团股份有限公司', key: '保利文化集团股份有限公司' },
    //   { label: '保利久联控股集团有限责任公司', key: '保利久联控股集团有限责任公司' },
    //   { label: '中国华信邮电科技有限公司', key: '中国华信邮电科技有限公司' },
    //   { label: '保利财务有限公司', key: '保利财务有限公司' },
    //   { label: '北京新保利大厦房地产开发有限公司', key: '北京新保利大厦房地产开发有限公司' },
    //   { label: '保利投资控股有限公司', key: '保利投资控股有限公司' },
    //   { label: '综合管理中心', key: '综合管理中心' },
    //   { label: '战略投资中心', key: '战略投资中心' },
    //   { label: '科技信息中心', key: '科技信息中心' },
    //   { label: '人力资源管理中心', key: '人力资源管理中心' },
    //   { label: '财务金融中心', key: '财务金融中心' },
    //   { label: '运营管理中心', key: '运营管理中心' },
    // ],
  },
  {
    label: '一级业务域',
    key: 'first_biz_domain',
    options: [
      { label: '运营管理', key: '运营管理' },
      { label: '人力资源', key: '人力资源' },
      { label: '战略投资', key: '战略投资' },
      { label: '财务金融', key: '财务金融' },
      { label: '风险内控与法律合规', key: '风险内控与法律合规' },
      { label: '科技信息', key: '科技信息' },
      { label: '党群管理', key: '党群管理' },
      { label: '综合办公', key: '综合办公' },
    ],
  },
  {
    label: '二级业务域',
    key: 'second_biz_domain',
    options: [],
  },
  {
    label: '三级业务域',
    key: 'third_biz_domain',
    options: [],
  },
  {
    label: '来源渠道类别',
    key: 'source_channel',
    options: [
      { label: '线上信息系统', key: '线上信息系统' },
      { label: '线下手工加工的报表文件', key: '线下手工加工的报表文件' },
      { label: '外部机构等其他数据来源渠道', key: '外部机构等其他数据来源渠道' },
    ],
  },

  {
    label: '更新频率',
    key: 'update_frequency',
    options: [
      { label: '实时更新', key: '实时更新' },
      { label: '每小时更新', key: '每小时更新' },
      { label: '每周更新', key: '每周更新' },
      { label: '每月更新', key: '每月更新' },
      { label: '每季度更新', key: '每季度更新' },
      { label: '每年更新', key: '每年更新' },
    ],
  },
  {
    label: '数据记录条数',
    key: 'amount_range',
    options: [
      { label: '百级以下', key: '百级以下' },
      { label: '百级至千级', key: '百级至千级' },
      { label: '千级至万级', key: '千级至万级' },
      { label: '万级至十万级', key: '万级至十万级' },
      { label: '十万级至百万级', key: '十万级至百万级' },
      { label: '百万级至千万级', key: '百万级至千万级' },
      { label: '千万级以上', key: '千万级以上' },
    ],
  },
  {
    label: '安全等级',
    key: 'security_level',
    options: [
      { label: '一般', key: '一般' },
      { label: '重要', key: '重要' },
      { label: '核心', key: '核心' },
    ],
  },
  {
    label: '数据库类型',
    key: 'db_type',
    options: [
      { label: 'Oracle', key: 'Oracle' },
      { label: 'Mysql', key: 'MySQL' },
      { label: 'HANA', key: 'HANA' },
      { label: 'DB2', key: 'DB2' },
      { label: 'Postgresql', key: 'PostgreSQL' },
      { label: '达梦', key: '达梦' },
      { label: '其他数据库类型', key: '其他数据库类型' },
      { label: '其他数据文件类型', key: '其他数据文件类型' },
    ],
  },
  // {
  //   label: '共享范围',
  //   key: 'share_type',
  //   options: [],
  //   fetchOptions: async () => {
  //     let options: FilterOptionItem[] = [];
  //     try {
  //       const response = await getOrgs();
  //       if (response.code === 200001) {
  //         const data: OrgsData[] = response.data || [];
  //         options = data.map((item) => ({
  //           label: item.name,
  //           key: item.name,
  //         }));
  //       }
  //     } catch (error) {}
  //     return options;
  //   },
  // },
];

const DOMAIN_CASCADED_OPTIONS = {
  运营管理: {
    second_biz_domain: [
      { label: '企业运营', key: '企业运营' },
      { label: '供应链管理', key: '供应链管理' },
      { label: '安全管理', key: '安全管理' },
      { label: '质量管理', key: '质量管理' },
      { label: '环保管理', key: '环保管理' },
      { label: '业绩考核', key: '业绩考核' },
    ],
    third_biz_domain: {
      企业运营: [{ label: '公司管理', key: '公司管理' }],
      供应链管理: [
        { label: '采购管理', key: '采购管理' },
        { label: '合同管理', key: '合同管理' },
        { label: '客户管理', key: '客户管理' },
        { label: '供应商管理', key: '供应商管理' },
        { label: '票据管理', key: '票据管理' },
        { label: '库存管理', key: '库存管理' },
      ],
      安全管理: [
        { label: '安全生产', key: '安全生产' },
        { label: '安全培训', key: '安全培训' },
      ],
      质量管理: [{ label: '服务质量', key: '服务质量' }],
      环保管理: [{ label: '能源生态', key: '能源生态' }],
      业绩考核: [{ label: '子公司经营业绩', key: '子公司经营业绩' }],
    },
  },
  人力资源: {
    second_biz_domain: [
      { label: '干部管理', key: '干部管理' },
      { label: '组织管理', key: '组织管理' },
      { label: '薪酬管理', key: '薪酬管理' },
      { label: '人才发展', key: '人才发展' },
      { label: '综合事务', key: '综合事务' },
    ],
    third_biz_domain: {
      干部管理: [
        { label: '干部管理', key: '干部管理' },
        { label: '干部监督', key: '干部监督' },
      ],
      组织管理: [
        { label: '单位管理', key: '单位管理' },
        { label: '部门管理', key: '部门管理' },
        { label: '员工管理', key: '员工管理' },
        { label: '岗位管理', key: '岗位管理' },
        { label: '职称管理', key: '职称管理' },
        { label: '职级管理', key: '职级管理' },
        { label: '任职管理', key: '任职管理' },
        { label: '培训管理', key: '培训管理' },
        { label: '党员管理', key: '党员管理' },
      ],
      薪酬管理: [
        { label: '员工薪酬', key: '员工薪酬' },
        { label: '绩效管理', key: '绩效管理' },
        { label: '福利管理', key: '福利管理' },
        { label: '薪酬成本', key: '薪酬成本' },
      ],
      人才发展: [
        { label: '人才单位管理', key: '人才单位管理' },
        { label: '人才资源管理', key: '人才资源管理' },
        { label: '人才培训管理', key: '人才培训管理' },
        { label: '人才变动管理', key: '人才变动管理' },
        { label: '人才招聘管理', key: '人才招聘管理' },
        { label: '处分变动管理', key: '处分变动管理' },
      ],
      综合事务: [
        { label: '涉外事务管理', key: '涉外事务管理' },
        { label: '干部事务管理', key: '干部事务管理' },
      ],
    },
  },
  战略投资: {
    second_biz_domain: [
      { label: '战略管理', key: '战略管理' },
      { label: '投资管理', key: '投资管理' },
      { label: '战略合作', key: '战略合作' },
    ],
    third_biz_domain: {
      战略管理: [
        { label: '战略规划管理', key: '战略规划管理' },
        { label: '战略资源管理', key: '战略资源管理' },
        { label: '[政策、行业、市场研究]', key: '政策行业市场研究' },
        { label: '深化改革管理', key: '深化改革管理' },
      ],
      投资管理: [
        { label: '投资项目管理', key: '投资项目管理' },
        { label: '资本运作管理', key: '资本运作管理' },
      ],
      战略合作: [
        { label: '战略合作管理', key: '战略合作管理' },
        { label: '战略交流管理', key: '战略交流管理' },
      ],
    },
  },
  财务金融: {
    second_biz_domain: [
      { label: '会计管理', key: '会计管理' },
      { label: '预算管理', key: '预算管理' },
      { label: '司库管理', key: '司库管理' },
    ],
    third_biz_domain: {
      会计管理: [
        { label: '财务分析管理', key: '财务分析管理' },
        { label: '合并报表管理', key: '合并报表管理' },
        { label: '税务核算管理', key: '税务核算管理' },
        { label: '会计核算管理', key: '会计核算管理' },
        { label: '会计出纳管理', key: '会计出纳管理' },
      ],
      预算管理: [
        { label: '全面预算管理', key: '全面预算管理' },
        { label: '产权管理', key: '产权管理' },
        { label: '财务绩效管理', key: '财务绩效管理' },
      ],
      司库管理: [
        { label: '资金管理', key: '资金管理' },
        { label: '金融业务管理', key: '金融业务管理' },
        { label: '账户管理', key: '账户管理' },
        { label: '结算管理', key: '结算管理' },
        { label: '金融机构贷款管理', key: '金融机构贷款管理' },
        { label: '债券融资管理', key: '债券融资管理' },
        { label: '票据管理', key: '票据管理' },
        { label: '担保管理', key: '担保管理' },
        { label: '供应链金融管理', key: '供应链金融管理' },
      ],
    },
  },
  风险内控与法律合规: {
    second_biz_domain: [
      { label: '风险管理', key: '风险管理' },
      { label: '内控管理', key: '内控管理' },
      { label: '法律合规', key: '法律合规' },
    ],
    third_biz_domain: {
      风险管理: [{ label: '风险管理', key: '风险管理' }],
      法律合规: [
        { label: '法治建设', key: '法治建设' },
        { label: '案件管理', key: '案件管理' },
        { label: '规章制度', key: '规章制度' },
        { label: '合规管理', key: '合规管理' },
      ],
    },
  },
  科技信息: {
    second_biz_domain: [
      { label: '科技创新管理', key: '科技创新管理' },
      { label: '信息化管理', key: '信息化管理' },
    ],
    third_biz_domain: {
      科技创新管理: [
        { label: '项目信息', key: '项目信息' },
        { label: '战略性新兴产业管理', key: '战略性新兴产业管理' },
        { label: '专利管理', key: '专利管理' },
        { label: '标准化管理', key: '标准化管理' },
        { label: '科技型企业管理', key: '科技型企业管理' },
        { label: '科技创新投资管理', key: '科技创新投资管理' },
      ],
      信息化管理: [
        { label: '基础设施管理', key: '基础设施管理' },
        { label: '系统运维管理', key: '系统运维管理' },
        { label: '网络安全', key: '网络安全' },
      ],
    },
  },
  综合办公: {
    second_biz_domain: [
      { label: '基础办公', key: '基础办公' },
      { label: '综合行政管理', key: '综合行政管理' },
    ],
    third_biz_domain: {
      基础办公: [
        { label: '行政管理', key: '行政管理' },
        { label: '办公安防', key: '办公安防' },
        { label: '会议系统', key: '会议系统' },
        { label: 'OA', key: 'OA' },
        { label: '门禁管理', key: '门禁管理' },
      ],
      综合行政管理: [
        { label: '行政管理', key: '行政管理' },
        { label: '办公安防', key: '办公安防' },
        { label: '会议系统', key: '会议系统' },
        { label: 'OA', key: 'OA' },
        { label: '门禁管理', key: '门禁管理' },
      ],
    },
  },
  党群管理: {
    second_biz_domain: [{ label: '党群管理', key: '党群管理' }],
    third_biz_domain: {
      党群管理: [{ label: '党群管理', key: '党群管理' }],
    },
  },
};

export type FiltersObj = Record<string, (string | number)[]>;
export type FiltersArray = [string, (string | number)[]];

export interface FilterOptionItem {
  label: string;
  key?: string | number;
  children?: FilterOptionItem[];
}

export interface FilterOption {
  label: string;
  multiple?: boolean;
  key?: string;
  options: FilterOptionItem[];
  fetchOptions?: () => Promise<FilterOptionItem[]>;
}
interface SearchBoxProps {
  className?: string;
  actions?: ReactNode | (() => ReactNode);
  searchTypeOptions: { label: string; key: string | number }[];
  filterLabelSuffix?: string;
  filterLabelWidth?: string | number;
  onSearchChange?: (search: string, searchType: string | number) => void;
  onFilterChange?: (filtersObj: FiltersObj, filtersArr: FiltersArray[]) => void;
  addonInputBefore?: (
    updateSelected: (option: FilterOption, value: string | number) => void,
    isSelected: (option: FilterOption, value: string | number) => void,
  ) => ReactNode;
  extraFilterOptions?: FilterOption[];
  defaultFilterOptions?: FilterOption[];
  defaultSelected?: FiltersObj;
}

export const SearchBox = ({
  className,
  actions,
  searchTypeOptions = [],
  filterLabelSuffix = '：',
  filterLabelWidth = '120px',
  onSearchChange,
  onFilterChange,
  addonInputBefore,
  extraFilterOptions,
  defaultFilterOptions,
  defaultSelected,
}: SearchBoxProps) => {
  const [selected, setSelected] = useState<FiltersObj>(defaultSelected ?? {});
  const [search, setSearch] = useState<string>('');
  const [searchInputValue, setSearchInputValue] = useState<string>('');
  const [searchType, setSearchType] = useState<string | number>(searchTypeOptions?.[0].key);
  const [filterOptions, setFilterOptions] = useState(defaultFilterOptions ?? FILTER_OPTIONS);
  const [showAllFilter, setShowAllFilter] = useState(false);
  const [maxShow] = useState(5);

  // 缓存计算后的有值的过滤器
  const hasValueFiltersArray = useMemo(
    () => Object.entries(selected).filter(([, value]) => !isNil(value) && !isEmpty(value)),
    [selected],
  );

  // 初始化filterOptions
  useEffect(() => {
    const fetchOptionsData = async () => {
      const promise = filterOptions.map(async (item) => {
        if (!item.fetchOptions) return item;
        const options = await item.fetchOptions();
        return {
          ...item,
          options,
        };
      });
      const newOptions = await Promise.all(promise);
      if (extraFilterOptions && extraFilterOptions.length > 0) {
        newOptions.push(...extraFilterOptions);
      }
      setFilterOptions(newOptions);
    };

    fetchOptionsData();
  }, []);

  useEffect(() => {
    const fetchOptionsData = async () => {
      const promise = filterOptions.map(async (item) => {
        if (!item.fetchOptions) return item;
        const options = await item.fetchOptions();
        return {
          ...item,
          options,
        };
      });
      const newOptions = await Promise.all(promise);
      setFilterOptions(newOptions);
    };

    fetchOptionsData();
  }, []);

  useEffect(() => {
    const firstBizDomain = selected.first_biz_domain?.[0] as keyof typeof DOMAIN_CASCADED_OPTIONS;
    const secondBizDomain = selected.second_biz_domain?.[0];

    const updatedFilterOptions = filterOptions.map((item) => {
      if (item.key === 'second_biz_domain') {
        if (firstBizDomain) {
          return {
            ...item,
            options: DOMAIN_CASCADED_OPTIONS[firstBizDomain]?.second_biz_domain ?? [],
          };
        }
        return {
          ...item,
          options: [],
        };
      }
      if (item.key === 'third_biz_domain') {
        if (secondBizDomain) {
          const firstChildren = DOMAIN_CASCADED_OPTIONS[firstBizDomain] || {};
          const thirdChildren = firstChildren.third_biz_domain || {};
          const options = thirdChildren[secondBizDomain as keyof typeof thirdChildren] || [];
          return {
            ...item,
            options,
          };
        }
        return {
          ...item,
          options: [],
        };
      }
      return item;
    });
    setFilterOptions(updatedFilterOptions as FilterOption[]);
  }, [selected.first_biz_domain, selected.second_biz_domain]);

  const handleFilterChange = useCallback(
    (newSelected: FiltersObj) => {
      if (isEqual(selected, newSelected)) return;
      if (onFilterChange) {
        const newFiltersArray = Object.entries(newSelected).filter(
          ([, value]) => !isNil(value) && !isEmpty(value),
        );
        const newFiltersObj = Object.fromEntries(newFiltersArray);
        onFilterChange(newFiltersObj, newFiltersArray);
      }
    },
    [selected],
  );

  useEffect(() => {
    if (defaultSelected && onFilterChange) {
      const newFiltersArray = Object.entries(defaultSelected).filter(
        ([, value]) => !isNil(value) && !isEmpty(value),
      );
      const newFiltersObj = Object.fromEntries(newFiltersArray);
      onFilterChange(newFiltersObj, newFiltersArray);
    }
  }, []);

  const onSearch = useCallback(() => {
    setSearch(searchInputValue);
    if (typeof onSearchChange === 'function') {
      onSearchChange(searchInputValue, searchType);
    }
  }, [searchInputValue, searchType, search, onSearchChange]);

  const clearSearch = useCallback(() => {
    setSearch('');
    setSearchInputValue('');
    onSearchChange?.('', searchType);
  }, [onFilterChange]);

  const handleRemove = useCallback(
    (key: string, v: string) => {
      let newSelected = {
        ...selected,
      };
      if (key === 'second_biz_domain') {
        newSelected = {
          ...selected,
          [key]: selected[key].filter((item) => item !== v),
          third_biz_domain: [],
        };
      } else if (key === 'first_biz_domain') {
        newSelected = {
          ...selected,
          [key]: selected[key].filter((item) => item !== v),
          second_biz_domain: [],
          third_biz_domain: [],
        };
      } else {
        newSelected = {
          ...selected,
          [key]: selected[key].filter((item) => item !== v),
        };
      }
      setSelected(newSelected);
      handleFilterChange(newSelected);
    },
    [selected, handleFilterChange],
  );

  // 点击全部
  const handleAllClick = useCallback(
    (item: FilterOption) => {
      const key = item.key!;
      let newSelected = {
        ...selected,
      };
      if (key === 'second_biz_domain') {
        newSelected = { ...selected, [key]: [], third_biz_domain: [] };
      } else if (key === 'first_biz_domain') {
        newSelected = {
          ...selected,
          [key]: [],
          second_biz_domain: [],
          third_biz_domain: [],
        };
      } else {
        newSelected = { ...selected, [key]: [] };
      }
      setSelected(newSelected);
      handleFilterChange(newSelected);
    },
    [selected, handleFilterChange],
  );

  const isSelected = useCallback(
    (option: FilterOption, value: string | number) => {
      const selectedValue = selected[option.key!] || [];
      return selectedValue.includes(value);
    },
    [selected],
  );

  const updateSelected = useCallback(
    (option: FilterOption, value: string | number) => {
      const key = option.key!;
      const selectedValue = selected[key] || [];

      let newSelected: FiltersObj = { ...selected };

      if (key === 'owner_dept_id') {
        // 只保存子级的 key
        newSelected[key] = option.multiple
          ? selectedValue.includes(value)
            ? selectedValue.filter((item) => item !== value)
            : [...selectedValue, value]
          : [value];
      } else {
        newSelected[key] = option.multiple
          ? selectedValue.includes(value)
            ? selectedValue.filter((item) => item !== value)
            : [...selectedValue, value]
          : [value];
      }

      if (isEqual(selected, newSelected)) return;
      setSelected(newSelected);
      handleFilterChange(newSelected);
    },
    [selected, handleFilterChange],
  );

  const formatValue = useCallback(
    (key: string, value: string | number) => {
      const option = filterOptions.find((option) => option.key === key);
      if (key === 'owner_dept_id') {
        // 遍历选项树，找到匹配的子选项
        const findChildOption = (options: FilterOptionItem[]): FilterOptionItem | undefined => {
          for (const opt of options) {
            if (opt.key === value) return opt;
            if (opt.children) {
              const childResult = findChildOption(opt.children);
              if (childResult) return childResult;
            }
          }
          return undefined;
        };
        const childOption = findChildOption(option?.options || []);
        return childOption?.label || '';
      }
      return option?.options.find((option) => option.key === value)?.label || '';
    },
    [filterOptions],
  );

  const showFiltersResult = useMemo(
    () => !!(search || hasValueFiltersArray.length > 0),
    [search, hasValueFiltersArray],
  );

  return (
    <>
      <div className="flex items-center ">
        {addonInputBefore?.(updateSelected, isSelected)}
        <Input
          className={cn(className, styles['search-box'], 'flex-1')}
          value={searchInputValue.trim()}
          onChange={(e) => setSearchInputValue(e.target.value)}
          addonBefore={
            <Select
              defaultValue={searchType}
              options={searchTypeOptions.map((item) => ({ ...item, value: item.key }))}
              onChange={setSearchType}
            />
          }
          addonAfter={
            <Button type="primary" icon={<SearchOutlined />} onClick={onSearch}>
              搜索
            </Button>
          }
        />
      </div>

      {actions ? (
        <div className="mt-5">{typeof actions === 'function' ? actions() : actions}</div>
      ) : null}

      {filterOptions.length > 0 && (
        <div className="flex items-end">
          <div className="mt-5 space-y-2 text-sm flex-1">
            {showFiltersResult && (
              <div className="flex gap-2">
                <div className="flex-shrink-0 flex-grow-0 p-1" style={{ width: filterLabelWidth }}>
                  筛选条件{filterLabelSuffix}
                </div>
                <div className="flex-1 flex gap-3 flex-wrap">
                  {search && (
                    <div className="p-1 bg-[#002fa50d] text-[#002fa5] flex items-center gap-2 rounded-sm">
                      <span>{search}</span>
                      <CloseOutlined className="w-3 h-3 cursor-pointer" onClick={clearSearch} />
                    </div>
                  )}
                  {hasValueFiltersArray.map(([key, value]) =>
                    value?.map((v) => (
                      <div
                        key={v}
                        className="p-1 bg-[#002fa50d] text-[#002fa5] flex items-center gap-2 rounded-sm"
                      >
                        <span>{formatValue(key, v)}</span>
                        <CloseOutlined
                          className="w-3 h-3 cursor-pointer"
                          onClick={() => handleRemove(key, v)}
                        />
                      </div>
                    )),
                  )}
                </div>
              </div>
            )}
            {filterOptions.map((item, index) => (
              <FilterItem
                className={`${showAllFilter ? 'flex' : index > maxShow - 1 ? 'hidden' : 'flex'}`}
                key={item.key}
                item={item}
                selected={selected}
                filterLabelWidth={filterLabelWidth}
                filterLabelSuffix={filterLabelSuffix}
                handleAllClick={handleAllClick}
                isSelected={isSelected}
                updateSelected={updateSelected}
              />
            ))}
          </div>
        </div>
      )}
      <div className="flex justify-center items-center py-4">
        <Button
          type="link"
          size="small"
          onClick={() => setShowAllFilter(!showAllFilter)}
          className="flex items-center justify-center"
        >
          {!showAllFilter ? '更多选项' : '收起更多'}
          <DownOutlined
            className={cn('transition-transform duration-500', {
              'rotate-180': showAllFilter,
            })}
          />
        </Button>
      </div>
    </>
  );
};

SearchBox.displayName = 'SearchBox';
