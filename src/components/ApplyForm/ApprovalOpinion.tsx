import { Col, Form, Input, Radio, Row, Steps } from 'antd';
import { type Rule } from 'antd/es/form';
import { useContext } from 'react';
import { Context } from '.';

const stepItems = [
  { title: '进行中', description: '拥有审批方', key: '拥有方' },
  { title: '待审批', description: '管理方审批', key: '管理方' },
  { title: '完成', key: '审核结束' },
];

// 审批意见
export const ApplyFormApprovalOpinion = ({
  currentNode,
  rules,
  showAdvice,
}: {
  currentNode?: string;
  rules?: Record<string, Rule[]>;
  showAdvice?: boolean;
}) => {
  const { itemGutter } = useContext(Context);
  return (
    <>
      <Row gutter={itemGutter}>
        <Col span={24}>
          <div className="w-2/4 mx-auto">
            <Form.Item name="current_node">
              <Steps
                current={currentNode ? stepItems.findIndex((item) => item.key === currentNode) : 0}
                items={stepItems}
              ></Steps>
            </Form.Item>
          </div>
        </Col>
        <Col span={24}>
          <Form.Item label="审核意见" name="pass" rules={rules?.pass}>
            <Radio.Group>
              <Radio value={true}>通过</Radio>
              <Radio value={false}>驳回</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        {showAdvice && (
          <Col span={24}>
            <Form.Item label="反馈意见" name="advice" rules={rules?.advice}>
              <Input.TextArea rows={3}>请输入反馈意见</Input.TextArea>
            </Form.Item>
          </Col>
        )}
        <Col span={24}>
          <Form.Item label="审批人姓名" name="approver" rules={rules?.approver}>
            <Input disabled />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="审批人职务" name="approver_position" rules={rules?.approver_position}>
            <Input disabled />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="审批日期" name="approved_at" rules={rules?.approved_at}>
            <Input disabled />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

ApplyFormApprovalOpinion.displayName = 'ApplyFormApprovalOpinion';
