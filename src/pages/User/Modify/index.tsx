import { GetVerificationCode, ModifyPassword } from '@/services/Login';
import { Helmet, history, useIntl } from '@umijs/max';
import { Button, Form, Input, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import Settings from '../../../../config/defaultSettings';

export default function Modify() {
  const intl = useIntl();

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [codeButtonDisabled, setCodeButtonDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const handleCodeButtonClick = async () => {
    const mobile = form.getFieldValue('mobile');
    try {
      const res = await GetVerificationCode(mobile);
      if (res.code === 200001) {
        message.success('验证码发送成功');
        setCodeButtonDisabled(true);
        setCountdown(60);

        countdownIntervalRef.current = setInterval(() => {
          setCountdown((prevCountdown) => {
            const newCountdown = prevCountdown - 1;
            if (newCountdown <= 0) {
              clearInterval(countdownIntervalRef.current!);
              setCodeButtonDisabled(false);
              countdownIntervalRef.current = null;
              return 0;
            }
            return newCountdown;
          });
        }, 1000);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error(JSON.stringify(error));
    }
  };

  useEffect(() => {
    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, []);

  return (
    <>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.register',
            defaultMessage: '修改密码',
          })}
          - {Settings.title}
        </title>
      </Helmet>

      <div className="bg-[#002FA50D] h-screen w-screen flex">
        <div className="w-[845px] h-[612px] m-auto bg-white p-10 flex flex-col justify-between">
          <div className="text-[25px] font-bold text-center leading-[44px]">修改密码</div>

          <Form
            form={form}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            size="large"
            autoComplete="off"
            onFinish={async (values: any) => {
              setLoading(true);
              if (values.password !== values.password_confirm) {
                message.error('两次输入的密码不一致');
                return;
              }

              const res = await ModifyPassword(values);
              if (res.code === 200001) {
                message.success('修改成功');
                history.push('/user/login');
                setLoading(false);
              } else {
                message.error(res.message);
                setLoading(false);
              }
            }}
          >
            <Form.Item
              label="手机号"
              name="mobile"
              rules={[
                { required: true, message: '请输入手机号' },
                {
                  pattern: /^1[3-9]\d{9}$/,
                  message: '请输入有效的手机号',
                },
              ]}
            >
              <div className="flex">
                <Input placeholder="请输入手机号" type="tel" />
                <Button
                  type="link"
                  disabled={codeButtonDisabled}
                  onClick={handleCodeButtonClick}
                  className="min-w-24"
                >
                  {countdown === 0 ? '获取验证码' : `${countdown}秒`}
                </Button>
              </div>
            </Form.Item>
            <Form.Item
              label="验证码"
              name="code"
              rules={[{ required: true, message: '请输入验证码' }]}
            >
              <Input placeholder="请输入验证码" />
            </Form.Item>
            <Form.Item
              label="密码"
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input placeholder="请输入密码" type="password" />
            </Form.Item>
            <Form.Item
              label="再次输入密码"
              name="password_confirm"
              rules={[
                { required: true, message: '请再次输入密码' },
                () => ({
                  validator(_: any, value: string) {
                    if (value !== form.getFieldValue('password')) {
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input placeholder="请再次输入密码" type="password" />
            </Form.Item>
          </Form>

          <div className="flex justify-center gap-12">
            <Button onClick={() => history.back()}>取消</Button>
            <Button
              type="primary"
              onClick={() => {
                form.submit();
              }}
              loading={loading}
            >
              确定修改
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
