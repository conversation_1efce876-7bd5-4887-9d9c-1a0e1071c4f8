import { ApplyForm } from '@/components/ApplyForm';
import { CascaderOption } from '@/components/ApplyForm/Base';
import { SubTitle } from '@/components/SubTitle';
import { getOrgsTree, OrgsTreeData, ResponseData } from '@/services/dataResources/orgsApi';
import {
  getResource,
  reportEdit,
  ResourceData,
  resourceReport,
} from '@/services/dataResources/reportApi';
import { useModel } from '@umijs/max';
import { Button, Form, message, Spin } from 'antd';
import { isArray } from 'lodash';
import { useEffect, useState } from 'react';
import { history, useLocation } from 'umi';

// const ownerDeptOptions = [
//   { label: '中国保利集团有限公司', value: '中国保利集团有限公司' },
//   { label: '保利国际控股有限公司', value: '保利国际控股有限公司' },
//   { label: '保利发展控股集团股份有限公司', value: '保利发展控股集团股份有限公司' },
//   { label: '广东省重工建筑设计院', value: '广东省重工建筑设计院' },
//   { label: '保利置业集团有限公司', value: '保利置业集团有限公司' },
//   { label: '中国轻工集团有限公司', value: '中国轻工集团有限公司' },
//   { label: '中国制浆造纸研究院有限公司', value: '中国制浆造纸研究院有限公司' },
//   { label: '中国食品发酵工业研究院有限公司', value: '中国食品发酵工业研究院有限公司' },
//   { label: '中国日用化学研究院有限公司', value: '中国日用化学研究院有限公司' },
//   { label: '中国皮革制鞋研究院有限公司', value: '中国皮革制鞋研究院有限公司' },
//   { label: '中轻日化科技有限公司', value: '中轻日化科技有限公司' },
//   { label: '中轻检验认证有限公司', value: '中轻检验认证有限公司' },
//   { label: '中国海诚工程科技股份有限公司', value: '中国海诚工程科技股份有限公司' },
//   { label: '中轻长泰（长沙）智能科技股份有限公司', value: '中轻长泰（长沙）智能科技股份有限公司' },
//   { label: '中国工艺集团有限公司', value: '中国工艺集团有限公司' },
//   { label: '保利文化集团股份有限公司', value: '保利文化集团股份有限公司' },
//   { label: '保利久联控股集团有限责任公司', value: '保利久联控股集团有限责任公司' },
//   { label: '中国华信邮电科技有限公司', value: '中国华信邮电科技有限公司' },
//   { label: '保利财务有限公司', value: '保利财务有限公司' },
//   { label: '北京新保利大厦房地产开发有限公司', value: '北京新保利大厦房地产开发有限公司' },
//   { label: '保利投资控股有限公司', value: '保利投资控股有限公司' },
//   { label: '综合管理中心', value: '综合管理中心' },
//   { label: '战略投资中心', value: '战略投资中心' },
//   { label: '科技信息中心', value: '科技信息中心' },
//   { label: '人力资源管理中心', value: '人力资源管理中心' },
//   { label: '财务金融中心', value: '财务金融中心' },
//   { label: '运营管理中心', value: '运营管理中心' },
//   { label: '其他数据来源', value: '其他数据来源' },
// ];

const sourceChannelOptions = [
  { label: '线上信息系统', value: '线上信息系统' },
  { label: '线下手工加工的报表文件', value: '线下手工加工的报表文件' },
  { label: '外部机构等其他数据来源渠道', value: '外部机构等其他数据来源渠道' },
];

const firstBizDomainOptions = [
  { label: '运营管理', value: '运营管理' },
  { label: '人力资源', value: '人力资源' },
  { label: '战略投资', value: '战略投资' },
  { label: '财务金融', value: '财务金融' },
  { label: '风险内控与法律合规', value: '风险内控与法律合规' },
  { label: '科技信息', value: '科技信息' },
  { label: '党群管理', value: '党群管理' },
  { label: '综合办公', value: '综合办公' },
];

const DOMAIN_CASCADED_OPTIONS = {
  运营管理: {
    second_biz_domain: [
      { label: '企业运营', value: '企业运营' },
      { label: '供应链管理', value: '供应链管理' },
      { label: '安全管理', value: '安全管理' },
      { label: '质量管理', value: '质量管理' },
      { label: '环保管理', value: '环保管理' },
      { label: '业绩考核', value: '业绩考核' },
    ],
    third_biz_domain: {
      企业运营: [{ label: '公司管理', value: '公司管理' }],
      供应链管理: [
        { label: '采购管理', value: '采购管理' },
        { label: '合同管理', value: '合同管理' },
        { label: '客户管理', value: '客户管理' },
        { label: '供应商管理', value: '供应商管理' },
        { label: '票据管理', value: '票据管理' },
        { label: '库存管理', value: '库存管理' },
      ],
      安全管理: [
        { label: '安全生产', value: '安全生产' },
        { label: '安全培训', value: '安全培训' },
      ],
      质量管理: [{ label: '服务质量', value: '服务质量' }],
      环保管理: [{ label: '能源生态', value: '能源生态' }],
      业绩考核: [{ label: '子公司经营业绩', value: '子公司经营业绩' }],
    },
  },
  人力资源: {
    second_biz_domain: [
      { label: '干部管理', value: '干部管理' },
      { label: '组织管理', value: '组织管理' },
      { label: '薪酬管理', value: '薪酬管理' },
      { label: '人才发展', value: '人才发展' },
      { label: '综合事务', value: '综合事务' },
    ],
    third_biz_domain: {
      干部管理: [
        { label: '干部管理', value: '干部管理' },
        { label: '干部监督', value: '干部监督' },
      ],
      组织管理: [
        { label: '单位管理', value: '单位管理' },
        { label: '部门管理', value: '部门管理' },
        { label: '员工管理', value: '员工管理' },
        { label: '岗位管理', value: '岗位管理' },
        { label: '职称管理', value: '职称管理' },
        { label: '职级管理', value: '职级管理' },
        { label: '任职管理', value: '任职管理' },
        { label: '培训管理', value: '培训管理' },
        { label: '党员管理', value: '党员管理' },
      ],
      薪酬管理: [
        { label: '员工薪酬', value: '员工薪酬' },
        { label: '绩效管理', value: '绩效管理' },
        { label: '福利管理', value: '福利管理' },
        { label: '薪酬成本', value: '薪酬成本' },
      ],
      人才发展: [
        { label: '人才单位管理', value: '人才单位管理' },
        { label: '人才资源管理', value: '人才资源管理' },
        { label: '人才培训管理', value: '人才培训管理' },
        { label: '人才变动管理', value: '人才变动管理' },
        { label: '人才招聘管理', value: '人才招聘管理' },
        { label: '处分变动管理', value: '处分变动管理' },
      ],
      综合事务: [
        { label: '涉外事务管理', value: '涉外事务管理' },
        { label: '干部事务管理', value: '干部事务管理' },
      ],
    },
  },
  战略投资: {
    second_biz_domain: [
      { label: '战略管理', value: '战略管理' },
      { label: '投资管理', value: '投资管理' },
      { label: '战略合作', value: '战略合作' },
    ],
    third_biz_domain: {
      战略管理: [
        { label: '战略规划管理', value: '战略规划管理' },
        { label: '战略资源管理', value: '战略资源管理' },
        { label: '[政策、行业、市场研究]', value: '政策行业市场研究' },
        { label: '深化改革管理', value: '深化改革管理' },
      ],
      投资管理: [
        { label: '投资项目管理', value: '投资项目管理' },
        { label: '资本运作管理', value: '资本运作管理' },
      ],
      战略合作: [
        { label: '战略合作管理', value: '战略合作管理' },
        { label: '战略交流管理', value: '战略交流管理' },
      ],
    },
  },
  财务金融: {
    second_biz_domain: [
      { label: '会计管理', value: '会计管理' },
      { label: '预算管理', value: '预算管理' },
      { label: '司库管理', value: '司库管理' },
    ],
    third_biz_domain: {
      会计管理: [
        { label: '财务分析管理', value: '财务分析管理' },
        { label: '合并报表管理', value: '合并报表管理' },
        { label: '税务核算管理', value: '税务核算管理' },
        { label: '会计核算管理', value: '会计核算管理' },
        { label: '会计出纳管理', value: '会计出纳管理' },
      ],
      预算管理: [
        { label: '全面预算管理', value: '全面预算管理' },
        { label: '产权管理', value: '产权管理' },
        { label: '财务绩效管理', value: '财务绩效管理' },
      ],
      司库管理: [
        { label: '资金管理', value: '资金管理' },
        { label: '金融业务管理', value: '金融业务管理' },
        { label: '账户管理', value: '账户管理' },
        { label: '结算管理', value: '结算管理' },
        { label: '金融机构贷款管理', value: '金融机构贷款管理' },
        { label: '债券融资管理', value: '债券融资管理' },
        { label: '票据管理', value: '票据管理' },
        { label: '担保管理', value: '担保管理' },
        { label: '供应链金融管理', value: '供应链金融管理' },
      ],
    },
  },
  风险内控与法律合规: {
    second_biz_domain: [
      { label: '风险管理', value: '风险管理' },
      { label: '内控管理', value: '内控管理' },
      { label: '法律合规', value: '法律合规' },
    ],
    third_biz_domain: {
      风险管理: [{ label: '风险管理', value: '风险管理' }],
      法律合规: [
        { label: '法治建设', value: '法治建设' },
        { label: '案件管理', value: '案件管理' },
        { label: '规章制度', value: '规章制度' },
        { label: '合规管理', value: '合规管理' },
      ],
    },
  },
  科技信息: {
    second_biz_domain: [
      { label: '科技创新管理', value: '科技创新管理' },
      { label: '信息化管理', value: '信息化管理' },
    ],
    third_biz_domain: {
      科技创新管理: [
        { label: '项目信息', value: '项目信息' },
        { label: '战略性新兴产业管理', value: '战略性新兴产业管理' },
        { label: '专利管理', value: '专利管理' },
        { label: '标准化管理', value: '标准化管理' },
        { label: '科技型企业管理', value: '科技型企业管理' },
        { label: '科技创新投资管理', value: '科技创新投资管理' },
      ],
      信息化管理: [
        { label: '基础设施管理', value: '基础设施管理' },
        { label: '系统运维管理', value: '系统运维管理' },
        { label: '网络安全', value: '网络安全' },
      ],
    },
  },
  综合办公: {
    second_biz_domain: [
      { label: '基础办公', value: '基础办公' },
      { label: '综合行政管理', value: '综合行政管理' },
    ],
    third_biz_domain: {
      基础办公: [
        { label: '行政管理', value: '行政管理' },
        { label: '办公安防', value: '办公安防' },
        { label: '会议系统', value: '会议系统' },
        { label: 'OA', value: 'OA' },
        { label: '门禁管理', value: '门禁管理' },
      ],
      综合行政管理: [
        { label: '行政管理', value: '行政管理' },
        { label: '办公安防', value: '办公安防' },
        { label: '会议系统', value: '会议系统' },
        { label: 'OA', value: 'OA' },
        { label: '门禁管理', value: '门禁管理' },
      ],
    },
  },
  党群管理: {
    second_biz_domain: [{ label: '党群管理', value: '党群管理' }],
    third_biz_domain: {
      党群管理: [{ label: '党群管理', value: '党群管理' }],
    },
  },
};

const securityLevelOptions = [
  { label: '一般', value: '一般' },
  { label: '重要', value: '重要' },
  { label: '核心', value: '核心' },
];

const shareTypeOptions = [
  { label: '有条件共享（脱敏，加密共享）', value: '有条件共享' },
  { label: '无条件共享（公开可使用）', value: '无条件共享' },
  { label: '不予共享', value: '不予共享' },
];

const sensitiveStrategyOptions = [
  { label: '无', value: '无' },
  { label: '脱敏', value: '脱敏' },
  { label: '加密', value: '加密' },
  { label: '其他', value: '其他' },
];

const updateFrequencyOptions = [
  { label: '实时更新', value: '实时更新' },
  { label: '每小时更新', value: '每小时更新' },
  { label: '每日更新', value: '每日更新' },
  { label: '每周更新', value: '每周更新' },
  { label: '每月更新', value: '每月更新' },
  { label: '每季度更新', value: '每季度更新' },
  { label: '每年更新', value: '每年更新' },
];

const bizScaleOptions = [
  { label: '百级以下', value: '百级以下', amount: 100 },
  { label: '百级至千级', value: '百级至千级', amount: 1000 },
  { label: '千级至万级', value: '千级至万级', amount: 10000 },
  { label: '万级至十万级', value: '万级至十万级', amount: 100000 },
  { label: '十万级至百万级', value: '十万级至百万级', amount: 1000000 },
  { label: '百万级至千万级', value: '百万级至千万级', amount: 10000000 },
  { label: '千万级以上', value: '千万级以上', amount: 100000000 },
];

const dbTypeOptions = [
  { label: '数据库产品Oracle', value: 'Oracle' },
  { label: '数据库产品MySQL', value: 'MySQL' },
  { label: '数据库产品HANA', value: 'HANA' },
  { label: '数据库产品DB2', value: 'DB2' },
  { label: '数据库产品PostgreSQL', value: 'PostgreSQL' },
  { label: '数据库产品达梦', value: '达梦' },
  { label: '其他数据库产品', value: '其他数据库类型' },
  { label: 'Excel文件存储', value: 'Excel' },
  { label: 'CSV文件存储', value: 'CSV' },
  { label: 'JSON文件存储', value: 'JSON' },
  { label: '其他文件存储', value: '其他数据文件类型' },
];

const storageTypeOptions = [
  { label: '结构化数据存储', value: '结构化数据存储' },
  { label: '半结构化数据存储', value: '半结构化数据存储' },
  { label: '非结构化数据存储', value: '非结构化数据存储' },
];

function findParentPathById(treeData: CascaderOption[], targetId: string) {
  let result: string[] = [];

  function searchTree(nodes: CascaderOption[], currentPath: string[]) {
    for (const node of nodes) {
      const newPath = [...currentPath, node.value];
      if (node.value === targetId) {
        result = newPath;
        return;
      }

      if (node.children) {
        searchTree(node.children, newPath);
      }
    }
  }

  searchTree(treeData, []);
  return result;
}

export default function Apply() {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const [form] = Form.useForm();
  const [showOwnerDeptDesc, setShowOwnerDeptDesc] = useState(false);
  const [showSensitiveStrategyDesc, setShowSensitiveStrategyDesc] = useState(false);
  const [secondBizDomainOptions, setSecondBizDomainOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [thirdBizDomainOptions, setThirdBizDomainOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [ownerDeptOptions, setOwnerDeptOptions] = useState<{ label: string; value: string }[]>([]);
  const [submiting, setSubmiting] = useState(false);
  // 是否为编辑模式
  const [id, setId] = useState('');
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);

  const localtion = useLocation();
  const [originDetailData, setOriginDetailData] = useState<{ owner_dept_id: string }>();

  useEffect(() => {
    const query = new URLSearchParams(location.search);
    const id = query.get('id');
    if (!id) return;
    setId(id);
    setIsEdit(true);
    const getData = async () => {
      setLoading(true);
      try {
        const response = await getResource(id);
        if (response.code !== 200001) {
          message.error(response.message);
          return;
        }
        const newData = {
          ...response.data,
        };
        if (!newData.owner_dept_id && newData.owner_dept_detail) {
          newData.owner_dept_id = 'other';
          setShowOwnerDeptDesc(true);
        }
        if (newData.sensitive_strategy === '其他') {
          setShowSensitiveStrategyDesc(true);
        }
        form.setFieldsValue(newData);
        setOriginDetailData(newData);
      } finally {
        setLoading(false);
      }
    };
    getData();
  }, [localtion]);

  useEffect(() => {
    const toOwnerData = (data: OrgsTreeData[]): CascaderOption[] => {
      return data.map((item) => ({
        label: item.name,
        value: item.id,
        children: item.children?.length ? toOwnerData(item.children) : undefined,
      }));
    };

    getOrgsTree().then((res: ResponseData<OrgsTreeData[]>) => {
      const ownerData = toOwnerData(res.data);

      if (currentUser.company_id === 'bb8d6171-bdc6-468d-b954-098d794fb6bd') {
        setOwnerDeptOptions([...ownerData, { label: '其他数据来源', value: 'other' }]);
      } else {
        setOwnerDeptOptions([
          ...ownerData.filter((item) => item.value === currentUser.company_id),
          { label: '其他数据来源', value: 'other' },
        ]);
      }
    });
  }, []);
  useEffect(() => {
    if (!originDetailData || ownerDeptOptions.length === 0) return;
    if (!originDetailData.id) return;
    const owner_dept_id = findParentPathById(ownerDeptOptions, originDetailData.owner_dept_id);
    form.setFieldValue('owner_dept_id', owner_dept_id);
  }, [ownerDeptOptions, originDetailData]);
  const onSubmit = async () => {
    setSubmiting(true);
    try {
      const values = await form.validateFields();
      console.log(values);

      if (isArray(values.owner_dept_id)) {
        values.owner_dept_id = values.owner_dept_id[values.owner_dept_id.length - 1];
      }
      if (values.owner_dept_id === 'other') {
        values.owner_dept_id = null;
      }
      if (values.biz_scale) {
        const currentBizObj = bizScaleOptions.find((item) => item.value === values.biz_scale);
        values.amount = currentBizObj?.amount;
      }
      if (values.data_items && values.data_items.length > 0) {
        values.data_items = values.data_items.map((item: any) => {
          if (item.id.includes('新建')) {
            return { ...item, id: '' };
          } else {
            return item;
          }
        });
      }
      let response: ResponseData<ResourceData>;
      if (isEdit && id) {
        // 更新
        response = await reportEdit(id, values);
      } else {
        // 上报
        response = await resourceReport(values);
      }
      if (response.code === 200001) {
        message.success('上报成功');
        history?.go(-1);
      } else {
        message.error(response.message);
      }
    } finally {
      setSubmiting(false);
    }
  };
  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 获取发生变化的字段名称
    const changedField = Object.keys(changedValues)[0];

    if (changedField === 'first_biz_domain') {
      // 清空二级和三级业务域
      form.setFieldsValue({
        second_biz_domain: undefined,
        third_biz_domain: undefined,
      });

      // 设置新的二级业务域选项
      const firstBizDomain = allValues.first_biz_domain as keyof typeof DOMAIN_CASCADED_OPTIONS;
      if (firstBizDomain) {
        setSecondBizDomainOptions(DOMAIN_CASCADED_OPTIONS[firstBizDomain].second_biz_domain);
      } else {
        setSecondBizDomainOptions([]);
      }
      setThirdBizDomainOptions([]); // 清空三级业务域选项
    }

    if (changedField === 'second_biz_domain') {
      // 清空三级业务域
      form.setFieldsValue({
        third_biz_domain: undefined,
      });

      if (allValues.second_biz_domain) {
        const first_biz_domain = allValues.first_biz_domain as keyof typeof DOMAIN_CASCADED_OPTIONS;
        const firstChildren = DOMAIN_CASCADED_OPTIONS[first_biz_domain] || {};
        const thirdChildren = firstChildren.third_biz_domain || {};
        const options =
          thirdChildren[allValues.second_biz_domain as keyof typeof thirdChildren] || [];

        setThirdBizDomainOptions(options);
      } else {
        setThirdBizDomainOptions([]);
      }
    }

    if (changedField === 'owner_dept_id') {
      console.log(allValues.owner_dept_id);
      // 根据选择的部门是否为 "其他数据来源" 来显示额外输入框
      setShowOwnerDeptDesc(allValues.owner_dept_id === 'other');
    }
    if (changedField === 'sensitive_strategy') {
      setShowSensitiveStrategyDesc(allValues.sensitive_strategy === '其他');
    }
  };
  return (
    <Spin spinning={loading}>
      <ApplyForm
        labelWidth="200px"
        labelAlign="left"
        itemGutter={60}
        itemSpan={12}
        size="small"
        form={form}
        onValuesChange={handleValuesChange}
      >
        <div className="text-right">
          <Button type="primary" onClick={onSubmit} loading={submiting}>
            {isEdit ? '更新' : '提交申请'}
          </Button>
        </div>
        <div className="text-center text-sm font-medium">
          {isEdit ? '资源目录信息编辑' : '资源目录上报申请'}
        </div>
        <SubTitle title="基础信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Base
            ownerDeptOptions={ownerDeptOptions}
            showOwnerDeptDesc={showOwnerDeptDesc}
            sourceChannelOptions={sourceChannelOptions}
            rules={{
              name: [
                {
                  required: true,
                },
              ],
              owner_dept_id: [
                {
                  required: true,
                },
              ],
              owner_dept_detail: [
                {
                  required: true,
                },
              ],
              source_channel: [
                {
                  required: true,
                },
              ],
              source_name: [
                {
                  required: true,
                },
              ],
            }}
          />
        </div>
        <SubTitle title="业务信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Business
            firstBizDomainOptions={firstBizDomainOptions}
            secondBizDomainOptions={secondBizDomainOptions}
            thirdBizDomainOptions={thirdBizDomainOptions}
            categoryOptions={[
              { label: '综合办公', value: '综合办公' },
              { label: '经营管理', value: '经营管理' },
              { label: '生产运营', value: '生产运营' },
              { label: '综合分类', value: '综合分类' },
            ]}
            rules={{
              first_biz_domain: [
                {
                  required: true,
                },
              ],
              second_biz_domain: [
                {
                  required: true,
                },
              ],
              third_biz_domain: [
                {
                  required: true,
                },
              ],
              category: [
                {
                  required: true,
                },
              ],
            }}
          />
        </div>
        <SubTitle title="技术信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Tech
            showSensitiveStrategyDesc={showSensitiveStrategyDesc}
            securityLevelOptions={securityLevelOptions}
            shareTypeOptions={shareTypeOptions}
            sensitiveStrategyOptions={sensitiveStrategyOptions}
            updateFrequencyOptions={updateFrequencyOptions}
            dbTypeOptions={dbTypeOptions}
            bizScaleOptions={bizScaleOptions}
            storageTypeOptions={storageTypeOptions}
            form={form}
            rules={{
              main_info: [
                {
                  required: true,
                },
              ],
              data_fields: [
                {
                  required: true,
                },
              ],
              usability_desc: [
                {
                  required: true,
                },
              ],
              security_level: [
                {
                  required: true,
                },
              ],
              share_type: [
                {
                  required: true,
                },
              ],
              sensitive_desc: [
                {
                  required: true,
                },
              ],
              sensitive_strategy: [
                {
                  required: true,
                },
              ],
              senstive_strategy_desc: [
                {
                  required: true,
                },
              ],
              update_frequency: [
                {
                  required: true,
                },
              ],
              biz_scale: [
                {
                  required: true,
                },
              ],
              db_type: [
                {
                  required: true,
                },
              ],
              db_name: [
                {
                  required: true,
                },
              ],
              table_name: [
                {
                  required: true,
                },
              ],
              storage_type: [
                {
                  required: true,
                },
              ],
            }}
          />
        </div>
        <SubTitle title="管理信息" />
        <div className="max-w-3xl mx-auto">
          <ApplyForm.Managent
            rules={{
              head_name: [
                {
                  required: true,
                },
              ],
              head_phone: [
                {
                  required: true,
                  pattern: /^1[3-9]\d{9}$/,
                  message: '请输入有效手机号',
                },
              ],
              head_email: [
                {
                  required: true,
                  pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: '请输入有效邮箱',
                },
              ],
            }}
          />
        </div>
      </ApplyForm>
    </Spin>
  );
}
