import { SubTitle } from '@/components/SubTitle';
import {
  getApplyMasterDataApprovalPage,
  getApplyMasterDataDetail,
  getResourceUseListItemApplicationDetail,
  getResourceUseListItemVettingProcess,
  modifyResourceUseListItemApplication,
  postApplyMasterDataApproval,
  postApplyMasterDataUpdate,
  ResourceUseResponse,
  submitResourceUseListItemVettingProcess,
} from '@/services/dataResources/applyApi';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Spin,
  Steps,
} from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { history } from 'umi';

const { TextArea } = Input;
const { RangePicker } = DatePicker;

export const VettingProcess = ({
  id,
  setIsShowModal,
  type,
  isMasterData,
}: {
  id?: string;
  setIsShowModal?: Dispatch<SetStateAction<boolean>>;
  type: string;
  isMasterData?: boolean;
}) => {
  const rules = { requiredField: [{ required: true, message: '必填项不能为空' }] };
  const isDisabled = type === '1';

  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [radioApproval, setRadioApproval] = useState(true);
  //主数据 detail
  const [masterData, setMasterData] = useState<any>({});

  const getVettingSteps = () => {
    const nodes = ['使用方', '拥有方', '管理方', '完成'];
    const currentStep = nodes.findIndex((node) => node === form.getFieldValue('current_node'));
    const steps = nodes.map((node, index) => {
      if (index === 3) {
        return {
          title: '完成',
          description: '完成审批',
        };
      }
      const getStepTitle = (idx: number, current: number) => {
        if (idx < current) return '完成';
        if (idx === current) return '进行中';
        return '待审批';
      };
      return {
        title: getStepTitle(index, currentStep),
        description: `${node}审批`,
      };
    });
    return { currentStep, steps };
  };

  const onVettingProcessFinish = async () => {
    if (!id) return;
    setLoading(true);
    const vettingAdvice = form.getFieldsValue([
      'advice',
      'approved_at',
      'approver',
      'approver_position',
      'pass',
    ]);
    if (!vettingAdvice.pass && !vettingAdvice.advice) {
      message.error('请填写审批意见');
      return;
    }
    try {
      const response: ResourceUseResponse<null> = await submitResourceUseListItemVettingProcess(
        id,
        vettingAdvice,
      );
      if (response.code !== 200001) {
        message.error(response.message);
        return;
      }
      message.success('提交成功');
      setIsShowModal?.(false);
      history.go(0);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  //主数据审批
  const onMasterVettingProcessFinish = async () => {
    if (!id) return;
    setLoading(true);
    const vettingAdvice = form.getFieldsValue([
      'advice',
      // 'approved_at',
      // 'approver',
      // 'approver_position',
      'pass',
    ]);
    if (!vettingAdvice.pass && !vettingAdvice.advice) {
      message.error('请填写审批意见');
      return;
    }
    try {
      const response: ResourceUseResponse<null> = await postApplyMasterDataApproval(
        id,
        vettingAdvice,
      );
      if (response.code !== 200001) {
        message.error(response.message);
        return;
      }
      message.success('操作成功');
      setIsShowModal?.(false);
      history.go(0);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  const onApplicationModifyFinish = async () => {
    if (!id) return;
    setLoading(true);
    const applicationModification = form.getFieldsValue([
      'collect_frequency',
      'collect_period',
      'is_agree',
      'remark',
      'req_desc',
      'security_step',
      'store_location',
    ]);
    const useRange = form.getFieldValue('use_range').split('~');
    try {
      const response: ResourceUseResponse<null> = await modifyResourceUseListItemApplication(id, {
        ...applicationModification,
        use_start_at: dayjs(useRange[0]).format('YYYY-MM-DD HH:mm:ss'),
        use_end_at: dayjs(useRange[1]).format('YYYY-MM-DD HH:mm:ss'),
      });
      if (response.code !== 200001) {
        message.error(response.message);
        return;
      }
      message.success('修改成功');
      setIsShowModal?.(false);
      history.go(0);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  //主数据update
  const onMasterApplicationModifyFinish = async () => {
    if (!id) return;
    setLoading(true);
    const applicationModification = form.getFieldsValue([
      'collect_frequency',
      'collect_period',
      'is_agree',
      'remark',
      'req_desc',
      'security_step',
      'store_location',
    ]);
    const useRange = form.getFieldValue('use_range').split('~');
    try {
      const response: ResourceUseResponse<null> = await postApplyMasterDataUpdate(id, {
        ...applicationModification,
        use_start_at: dayjs(useRange[0]).format('YYYY-MM-DD HH:mm:ss'),
        use_end_at: dayjs(useRange[1]).format('YYYY-MM-DD HH:mm:ss'),
        majorData_id: masterData.majorData_id,
        approval_company_id: masterData.approval_company_id,
      });
      if (response.code !== 200001) {
        message.error(response.message);
        return;
      }
      message.success('修改成功');
      setIsShowModal?.(false);
      history.go(0);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    if (isMasterData) {
      (async () => {
        setLoading(true);
        try {
          let response;
          if (type === '0') {
            response = await getApplyMasterDataDetail(id);
          } else {
            response = await getApplyMasterDataApprovalPage(id);
          }
          if (response.code !== 200001) {
            message.error(response.message);
            return;
          }
          setMasterData(response.data);
          form.setFieldsValue({
            ...response.data,
            use_range: response.data.use_start_at + '~' + response.data.use_end_at,
          });
        } catch (error) {
          message.error(JSON.stringify(error));
        } finally {
          setLoading(false);
        }
      })();
    } else {
      (async () => {
        setLoading(true);
        try {
          let response;
          if (type === '0') {
            response = await getResourceUseListItemApplicationDetail(id);
          } else {
            response = await getResourceUseListItemVettingProcess(id);
          }
          if (response.code !== 200001) {
            message.error(response.message);
            return;
          }
          form.setFieldsValue({
            ...response.data,
            use_range: response.data.use_start_at + '~' + response.data.use_end_at,
          });
        } catch (error) {
          message.error(JSON.stringify(error));
        } finally {
          setLoading(false);
        }
      })();
    }
  }, [id]);

  return (
    <Spin spinning={loading}>
      <Form
        labelCol={{ flex: '180px' }}
        form={form}
        labelAlign="right"
        onFinish={
          isMasterData
            ? type === '0'
              ? onMasterApplicationModifyFinish
              : onMasterVettingProcessFinish
            : type === '0'
            ? onApplicationModifyFinish
            : onVettingProcessFinish
        }
      >
        <SubTitle title="申请人信息" />
        <div className="max-w-3xl mx-auto">
          <Row gutter={12}>
            <Col span={12}>
              <Form.Item label="单位名称" name="applicant_company" rules={rules.requiredField}>
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="所属部门/团队" name="applicant_dept" rules={rules.requiredField}>
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={12}>
              <Form.Item label="姓名" name="applicant_name" rules={rules.requiredField}>
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="职位" name="applicant_position" rules={rules.requiredField}>
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={12}>
              <Form.Item label="联系方式" name="applicant_mobile" rules={rules.requiredField}>
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="申请日期" name="applicant_date" rules={rules.requiredField}>
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <SubTitle title="使用信息" />
        <div className="max-w-3xl mx-auto">
          <Row gutter={12}>
            {!isMasterData && (
              <Col span={12}>
                <Form.Item label="资源编号" name="resource_num" rules={rules.requiredField}>
                  <Input disabled />
                </Form.Item>
              </Col>
            )}
            <Col span={12}>
              <Form.Item
                label="资源名称"
                name={isMasterData ? 'major_data_name' : 'resource_name'}
                rules={rules.requiredField}
              >
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item label="需求描述" name="req_desc" rules={rules.requiredField}>
                <TextArea disabled={isDisabled} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item label="采集频次" name="collect_frequency" rules={rules.requiredField}>
                <Select
                  disabled={isDisabled}
                  options={[
                    { value: '实时', label: '实时' },
                    { value: '每小时', label: '每小时' },
                    { value: '每日', label: '每日' },
                    { value: '每周一', label: '每周一' },
                    { value: '每月1号', label: '每月1号' },
                    { value: '每季度（1、4、7、10月1号)', label: '每季度（1、4、7、10月1号)' },
                    { value: '每年（1月1号）', label: '每年（1月1号）' },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item
                label="采集时段"
                name="collect_period"
                rules={rules.requiredField}
                getValueProps={(value) => {
                  if (!value) return;
                  const [start, end] = value.split('~');
                  return {
                    value: [dayjs(start), dayjs(end)],
                  };
                }}
                normalize={(value) => {
                  if (Array.isArray(value)) {
                    return (
                      dayjs(value[0]).format('YYYY-MM-DD HH:mm') +
                      ' ~ ' +
                      dayjs(value[1]).format('YYYY-MM-DD HH:mm')
                    );
                  }
                  return value;
                }}
              >
                <RangePicker
                  disabled={isDisabled}
                  format="YYYY-MM-DD HH:mm"
                  showTime={{ format: 'HH:mm' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <SubTitle title="安全合规信息" />
        <div className="max-w-3xl mx-auto">
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item label="数据使用合规行声明" required>
                <div>{form.getFieldValue('is_agree') ? '已同意' : '不同意'}</div>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item label="数据安全措施" name="security_step" rules={rules.requiredField}>
                <Input disabled={isDisabled} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item
                label="预期使用时间"
                name="use_range"
                rules={rules.requiredField}
                getValueProps={(value) => {
                  if (!value) return;
                  const [start, end] = value.split('~');
                  return {
                    value: [dayjs(start), dayjs(end)],
                  };
                }}
                normalize={(value) => {
                  if (Array.isArray(value)) {
                    return (
                      dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss') +
                      ' ~ ' +
                      dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss')
                    );
                  }
                  return value;
                }}
              >
                <RangePicker disabled={isDisabled} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item
                label="数据存储和处理地点"
                name="store_location"
                rules={rules.requiredField}
              >
                <Input disabled={isDisabled} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item label="备注" name="remark">
                <Input disabled={isDisabled} />
              </Form.Item>
            </Col>
          </Row>
        </div>

        {type === '1' && (
          <>
            <SubTitle title="审批意见" />
            <div className="max-w-3xl mx-auto">
              <Row gutter={12}>
                <Col span={24}>
                  <Form.Item>
                    <Steps
                      progressDot
                      current={getVettingSteps().currentStep}
                      items={getVettingSteps().steps}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={12}>
                <Col span={24}>
                  <Form.Item label="审批意见" name="pass" rules={rules.requiredField}>
                    <Radio.Group onChange={(value) => setRadioApproval(value.target.value)}>
                      <Radio value={true}>通过</Radio>
                      <Radio value={false}>驳回</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={12}>
                <Col span={24}>
                  {!radioApproval && (
                    <Form.Item label="反馈意见" name="advice">
                      <TextArea />
                    </Form.Item>
                  )}
                </Col>
              </Row>
              <Row gutter={12}>
                <Col span={24}>
                  <Form.Item label="审批人姓名" name="approver" rules={rules.requiredField}>
                    <Input disabled />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={12}>
                <Col span={24}>
                  <Form.Item
                    label="审批人职务"
                    name="approver_position"
                    rules={rules.requiredField}
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={12}>
                <Col span={24}>
                  <Form.Item label="审批日期" name="approved_at" rules={rules.requiredField}>
                    <Input disabled />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </>
        )}

        <div className="text-center">
          <Button
            className="mr-2"
            onClick={() => {
              setIsShowModal?.(false);
            }}
          >
            取消
          </Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            确认
          </Button>
        </div>
      </Form>
    </Spin>
  );
};
