html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

//资源列表 详情 折叠面板
.list_detail_collapse {
  //card
  .ant-card .ant-card-body {
    padding: 20px;
  }
  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
    background: #002fa50d;
  }
  :where(.css-dev-only-do-not-override-1wq3r5n).ant-collapse-borderless
    > .ant-collapse-item
    > .ant-collapse-content
    > .ant-collapse-content-box {
    padding: 20px 20px 18px 24px;
  }
  .ant-collapse-borderless
    > .ant-collapse-item
    > .ant-collapse-content
    > .ant-collapse-content-box {
    padding-top: 16px;
  }

  //描述状态
  .ant-descriptions-item-label {
    font-size: 16px;
  }
  .ant-descriptions-item-content {
    font-size: 16px;
  }
}

//资源列表 详情 使用申请表单调整
.detail_application_form_sqrxx {
  .ant-form-item .ant-form-item-label > label {
    min-width: 151px;
  }
  :where(.css-dev-only-do-not-override-1r0lb53).ant-picker {
    width: 100%;
  }
}

//资源列表 编辑 使用申请表单调整
.edit_application_form_sqrxx {
  margin: 40px 0;
  .ant-form-item .ant-form-item-label > label {
    min-width: 180px;
  }
}
//资源更新动态列表
.zygx-table {
  .ant-pro-card-border {
    border: none;
  }
  .ant-pro-table-search {
    border: 1px solid rgba(5, 5, 5, 0.06);
  }
  .ant-pro-card .ant-pro-card-body {
    padding-inline: 0;
  }
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

.ant-pro-global-footer {
  margin-block-start: 8px;
  margin-block-end: 8px;
}
