// 接口
import { request } from 'umi';

export interface ResponseData<T> {
  code: number;
  data: T;
  message: string;
}

/**
 * 列表
 */
export interface OrgsQueryParams {
  /**
   * 组织类型：company、dept
   */
  type?: 'company' | 'dept';
  [property: string]: any;
}

export interface OrgsData {
  /**
   * 组织 id
   */
  id: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 上级组织 id
   */
  parent_id: string;
  /**
   * 组织类型
   */
  type: string;
  [property: string]: any;
}

export function getOrgs(params?: OrgsQueryParams) {
  return request('/api/v1/orgs', {
    params,
  });
}

export interface OrgsTreeData {
  /**
   * 下级公司列表
   */
  children: OrgsTreeData[];
  /**
   * 公司 id
   */
  id: string;
  /**
   * 公司名称
   */
  name: string;
  /**
   * 上级公司 id
   */
  parent_id: string;
  [property: string]: any;
}

export function getOrgsTree(isList?: boolean) {
  return request(`/api/v1/orgs/tree${isList ? '?is_list=true' : ''}`);
}
