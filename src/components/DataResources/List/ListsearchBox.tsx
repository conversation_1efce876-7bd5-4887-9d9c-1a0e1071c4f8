import { CloseOutlined, DownOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Input, Select } from 'antd';
import cn from 'classnames';
import { isEmpty, isNil } from 'lodash';
import { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './listSearchBox.module.less';

type FiltersObj = Record<string, (string | number)[]>;
type FiltersArray = [string, (string | number)[]];

interface FilterOption {
  label: string;
  multiple?: boolean;
  key?: string;
  options: {
    label: string;
    key?: string | number;
  }[];
}
interface SearchBoxProps {
  className?: string;
  tabsViewValue: string;
  actions?: ReactNode | (() => ReactNode);
  searchTypeOptions: { label: string; key: string | number }[];
  filterLabelSuffix?: string;
  filterLabelWidth?: string | number;
  filterOptions?: FilterOption[];
  onSearchChange?: (search: string, searchType: string | number) => void;
  onFilterChange?: (filtersObj: FiltersObj, filtersArr: FiltersArray[]) => void;
}

export const ListSearchBox = ({
  className,
  tabsViewValue,
  actions,
  searchTypeOptions = [],
  filterLabelSuffix = '：',
  filterLabelWidth = '120px',
  filterOptions = [],
  onSearchChange,
  onFilterChange,
}: SearchBoxProps) => {
  //展开收起
  const [expand, setExpand] = useState<boolean>(false);
  const [selected, setSelected] = useState<FiltersObj>({});
  const [search, setSearch] = useState<string>('');
  const [searchInputValue, setSearchInputValue] = useState<string>('');
  const [searchType, setSearchType] = useState<string | number>(searchTypeOptions?.[0].key);
  const mounted = useRef(false);

  // 缓存计算后的有值的过滤器
  const hasValueFiltersArray = useMemo(
    () => Object.entries(selected).filter(([, value]) => !isNil(value) && !isEmpty(value)),
    [selected],
  );

  const hasValueFiltersObj = useMemo(
    () => Object.fromEntries(hasValueFiltersArray),
    [hasValueFiltersArray],
  );

  useEffect(() => {
    if (!mounted.current) {
      mounted.current = true;
      return;
    }
    if (onFilterChange) {
      onFilterChange(hasValueFiltersObj, hasValueFiltersArray);
    }
  }, [selected, onFilterChange, hasValueFiltersObj, hasValueFiltersArray]);

  const onSearch = useCallback(() => {
    // 防止重复搜索相同的内容
    if (searchInputValue.trim() === search) return;
    setSearch(searchInputValue);
    if (typeof onSearchChange === 'function') {
      onSearchChange(searchInputValue, searchType);
    }
  }, [searchInputValue, searchType, search, onSearchChange]);

  const clearSearch = useCallback(() => {
    setSearch('');
    setSearchInputValue('');
  }, []);

  const _filterOptions = useMemo(() => {
    return filterOptions.map((item) => ({
      ...item,
      key: item.key || item.label,
      options: item.options.map((option) => ({
        ...option,
        key: option.key || option.label,
      })),
    }));
  }, [filterOptions]);

  const isSelected = useCallback(
    (option: FilterOption, value: string | number) => {
      const selectedValue = selected[option.key!] || [];
      return selectedValue.includes(value);
    },
    [selected],
  );

  const updateSelected = useCallback(
    (option: FilterOption, value: string | number) => {
      const key = option.key!;
      const selectedValue = selected[key] || [];

      setSelected((prevSelected) => ({
        ...prevSelected,
        [key]: option.multiple
          ? selectedValue.includes(value)
            ? selectedValue.filter((item) => item !== value)
            : [...selectedValue, value]
          : [value],
      }));
    },
    [selected],
  );

  const formatValue = useCallback(
    (key: string, value: string | number) => {
      const option = _filterOptions.find((option) => option.key === key);
      return option?.options.find((option) => option.key === value)?.label || '我管理的';
    },
    [_filterOptions],
  );

  const showFiltersResult = useMemo(
    () => !!(search || hasValueFiltersArray.length > 0),
    [search, hasValueFiltersArray],
  );

  return (
    <>
      <div className="flex items-center">
        {tabsViewValue === '资源列表' && (
          <Button
            className={cn('mr-5 cursor-pointer hover:bg-[#002fa50d]', {
              'text-[#002fa5] border-[#002fa5]': isSelected(
                {
                  label: '我管理的',
                  key: '0',
                  options: [{ label: '我管理的', key: 'wgld' }],
                },
                'wgld',
              ),
            })}
            onClick={() =>
              updateSelected(
                {
                  label: '我管理的',
                  key: '0',
                  options: [{ label: '我管理的', key: 'wgld' }],
                },
                'wgld',
              )
            }
          >
            我管理的
          </Button>
        )}
        <Input
          className={cn(className, styles['list-Search-box'])}
          value={searchInputValue.trim()}
          onChange={(e) => setSearchInputValue(e.target.value)}
          addonBefore={
            <Select
              defaultValue={searchType}
              options={searchTypeOptions.map((item) => ({ ...item, value: item.key }))}
              onChange={setSearchType}
            />
          }
          addonAfter={
            <Button type="primary" icon={<SearchOutlined />} onClick={onSearch}>
              搜索
            </Button>
          }
        />
      </div>
      {actions ? (
        <div className="mt-5">{typeof actions === 'function' ? actions() : actions}</div>
      ) : null}
      {_filterOptions.length > 0 && tabsViewValue === '资源列表' && (
        <div className={`mt-5 space-y-2 text-sm relative`}>
          {showFiltersResult && (
            <div className="flex gap-2">
              <div className="flex-shrink-0 flex-grow-0 p-1" style={{ width: filterLabelWidth }}>
                筛选条件{filterLabelSuffix}
              </div>
              <div className="flex-1 flex gap-3 flex-wrap">
                {search && (
                  <div className="p-1 bg-[#002fa50d] text-[#002fa5] flex items-center gap-2">
                    <span>{search}</span>
                    <CloseOutlined className="w-3 h-3 cursor-pointer" onClick={clearSearch} />
                  </div>
                )}
                {hasValueFiltersArray.map(([key, value]) =>
                  value.map((v) => (
                    <div
                      key={v}
                      className="p-1 bg-[#002fa50d] text-[#00A511] flex items-center gap-2 "
                    >
                      <span>{formatValue(key, v)}</span>
                      <CloseOutlined
                        className="w-3 h-3 cursor-pointer text-[#002fa5]"
                        onClick={() =>
                          setSelected((prev) => ({
                            ...prev,
                            [key]: prev[key].filter((item) => item !== v),
                          }))
                        }
                      />
                    </div>
                  )),
                )}
              </div>
            </div>
          )}
          {_filterOptions.map((item, index) => (
            <div
              className={`flex gap-2 transition duration-500 ${
                index > 3 ? (expand ? '' : 'hidden') : ''
              }`}
              key={item.key}
            >
              <div className="flex-shrink-0 flex-grow-0 p-1" style={{ width: filterLabelWidth }}>
                {item.label}
                {filterLabelSuffix}
              </div>
              <div className="flex-1 flex gap-3 text-gray-500">
                <div
                  className={cn('cursor-pointer p-1 hover:bg-[#002fa50d]', {
                    'bg-[#002fa50d] text-[#002fa5]':
                      isNil(selected[item.key]) || isEmpty(selected[item.key]),
                  })}
                  onClick={() => setSelected({ ...selected, [item.key]: [] })}
                >
                  全部
                </div>
                {item?.options.map((option) => (
                  <div
                    key={option.key}
                    className={cn('cursor-pointer p-1 hover:bg-[#002fa50d]', {
                      'bg-[#002fa50d] text-[#002fa5]': isSelected(item, option.key),
                    })}
                    onClick={() => updateSelected(item, option.key)}
                  >
                    {option.label}
                  </div>
                ))}
              </div>
            </div>
          ))}
          {
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    defaultBg: '#002fa50d',
                    defaultColor: '#002fa5',
                  },
                },
              }}
            >
              <Button
                size="small"
                className="absolute right-[6px] bottom-[6px]"
                onClick={() => setExpand(!expand)}
              >
                更多选项
                <DownOutlined
                  style={{
                    transform: expand ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: ' transform .5s',
                  }}
                />
              </Button>
            </ConfigProvider>
          }
        </div>
      )}
    </>
  );
};

ListSearchBox.displayName = 'ListSearchBox';
