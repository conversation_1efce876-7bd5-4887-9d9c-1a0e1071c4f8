import { Col, Form, Row, Select, Tooltip } from 'antd';
import { type Rule } from 'antd/es/form';
import { type DefaultOptionType } from 'antd/es/select';
import { useContext } from 'react';
import { Context } from '.';
import { InfoCircleOutlined } from '@ant-design/icons';

interface ApplyFormBusinessProps {
  rules?: Record<string, Rule[]>;
  firstBizDomainOptions?: DefaultOptionType[];
  secondBizDomainOptions?: DefaultOptionType[];
  thirdBizDomainOptions?: DefaultOptionType[];
  categoryOptions?: DefaultOptionType[];
  disabled?: boolean;
}

// 业务信息
export const ApplyFormBusiness = ({
  rules,
  firstBizDomainOptions,
  secondBizDomainOptions,
  thirdBizDomainOptions,
  disabled,
  categoryOptions,
}: ApplyFormBusinessProps) => {
  const { itemSpan, itemGutter } = useContext(Context);
  return (
    <>
      <Row gutter={itemGutter}>
        <Col span={itemSpan}>
          <Form.Item
            name="category"
            label={
              <div className="flex items-center gap-2">
                数据资源归属分类
                <Tooltip title="主要依据系统分类为参考">
                  <InfoCircleOutlined />
                </Tooltip>
              </div>
            }
            rules={rules?.category}
          >
            <Select
              placeholder="请选择数据资源归属分类"
              options={categoryOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="first_biz_domain" label="一级业务域" rules={rules?.first_biz_domain}>
            <Select
              placeholder="请选择一级业务域"
              options={firstBizDomainOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="second_biz_domain" label="二级业务域" rules={rules?.second_biz_domain}>
            <Select
              placeholder="请选择二级业务域"
              options={secondBizDomainOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="third_biz_domain" label="三级业务域" rules={rules?.third_biz_domain}>
            <Select
              placeholder="请选择三级业务域"
              options={thirdBizDomainOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

ApplyFormBusiness.displayName = 'ApplyFormBusiness';
