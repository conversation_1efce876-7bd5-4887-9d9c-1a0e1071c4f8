import { InfoCircleOutlined } from '@ant-design/icons';
import { Col, Form, Input, Radio, Row, Select, Tooltip } from 'antd';
import { type Rule } from 'antd/es/form';
import { type DefaultOptionType } from 'antd/es/select';
import { useContext } from 'react';
import { Context } from '.';
import { ModalTable } from './ModalTable';

interface ApplyFormTechProps {
  showSensitiveStrategyDesc?: boolean;
  rules?: Record<string, Rule[]>;
  securityLevelOptions?: DefaultOptionType[];
  shareTypeOptions?: DefaultOptionType[];
  updateFrequencyOptions?: DefaultOptionType[];
  dbTypeOptions?: DefaultOptionType[];
  sensitiveStrategyOptions?: DefaultOptionType[];
  bizScaleOptions?: DefaultOptionType[];
  storageTypeOptions?: DefaultOptionType[];
  disabled?: boolean;
  approvalView?: boolean;
  form?: any;
}

// 技术信息
export const ApplyFormTech = ({
  showSensitiveStrategyDesc,
  rules,
  securityLevelOptions,
  shareTypeOptions,
  updateFrequencyOptions,
  dbTypeOptions,
  sensitiveStrategyOptions,
  bizScaleOptions,
  storageTypeOptions,
  disabled,
  // 是否为审批视图
  approvalView,
  form,
}: ApplyFormTechProps) => {
  const { itemSpan, itemGutter } = useContext(Context);
  return (
    <>
      {!approvalView && (
        <Row gutter={itemGutter}>
          <Col span={24}>
            <Form.Item
              name="main_info"
              label={
                <div className="flex items-center gap-2">
                  主要数据信息
                  <Tooltip title="主要包含的数据信息说明，具体说明数据中包含哪些字段，或者主要的信息内容摘要。">
                    <InfoCircleOutlined />
                  </Tooltip>
                </div>
              }
              rules={rules?.main_info}
            >
              <Radio.Group disabled={disabled}>
                <Radio value="线上信息系统">线上信息系统</Radio>
                <Radio value="线下手工报表">线下手工报表</Radio>
                <Radio value="其他数据来源">其他数据来源</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="data_fields"
              label="&nbsp;"
              colon={false}
              rules={rules?.data_fields}
              required={false}
            >
              <Input.TextArea
                disabled={disabled}
                rows={2}
                showCount
                maxLength={500}
                placeholder="请输入数据资源主要包含哪些字段。例：员工基本信息表，主要包括如员工姓名、员工编号、员工性别、员工身份证号、出生日期等基本属性信息字段。"
              />
            </Form.Item>
          </Col>
        </Row>
      )}
      <Row gutter={itemGutter}>
        {approvalView && (
          <Col span={itemSpan}>
            <Form.Item name="main_info" label="主要数据信息" rules={rules?.main_info}>
              <Input disabled={disabled} />
            </Form.Item>
          </Col>
        )}
        <Col span={24}>
          <Form.Item shouldUpdate label="数据项信息" rules={rules?.security_level}>
            {() => {
              return (
                <Form.Item name="data_items">
                  <ModalTable
                    mainForm={form}
                    approvalView={approvalView}
                    value={form?.getFieldValue('data_items') && form?.getFieldValue('data_items')}
                  />
                  <span className="ml-[20px] text-[#999]">
                    已录入{' '}
                    {form?.getFieldValue('data_items')
                      ? form?.getFieldValue('data_items')?.length
                      : 0}{' '}
                    条数据项
                  </span>
                </Form.Item>
              );
            }}
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="security_level" label="数据安全等级" rules={rules?.security_level}>
            <Select
              placeholder="请选择安全等级"
              options={securityLevelOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item
            name="share_type"
            label={
              <div className="flex items-center gap-2">
                开放共享类型
                <Tooltip title="对集团内部其他公司部门的共享策略">
                  <InfoCircleOutlined />
                </Tooltip>
              </div>
            }
            rules={rules?.share_type}
          >
            <Select
              placeholder="请选择开放共享类型"
              options={shareTypeOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="sensitive_desc"
            label={
              <div className="flex items-center gap-2">
                敏感信息强调说明
                <Tooltip title="说明数据资源中是否包含敏感信息，以及哪些为敏感信息">
                  <InfoCircleOutlined />
                </Tooltip>
              </div>
            }
            rules={rules?.sensitive_desc}
          >
            <Input.TextArea rows={2} placeholder="请输入敏感信息强调说明" disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item
            name="sensitive_strategy"
            label={
              <div className="flex items-center gap-2">
                敏感信息采集或共享策略
                <Tooltip title="说明使用资源者应对敏感信息作何种处理">
                  <InfoCircleOutlined />
                </Tooltip>
              </div>
            }
            rules={rules?.sensitive_strategy}
          >
            <Select
              placeholder="请选择采集或共享策略"
              options={sensitiveStrategyOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        {showSensitiveStrategyDesc && (
          <Col span={itemSpan}>
            <Form.Item
              name="senstive_strategy_desc"
              label="采取策略说明"
              rules={rules?.senstive_strategy_desc}
            >
              <Input placeholder="请填写采取策略说明" disabled={disabled} />
            </Form.Item>
          </Col>
        )}
        <Col span={itemSpan}>
          <Form.Item
            name="update_frequency"
            label={
              <div className="flex items-center gap-2">
                更新频率
                <Tooltip title="业务数据的更新周期">
                  <InfoCircleOutlined />
                </Tooltip>
              </div>
            }
            rules={rules?.update_frequency}
          >
            <Select
              placeholder="请选择更新频率"
              options={updateFrequencyOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item
            name="biz_scale"
            label={
              <div className="flex items-center gap-2">
                业务规模
                <Tooltip title="数据资源对应业务的规模">
                  <InfoCircleOutlined />
                </Tooltip>
              </div>
            }
            rules={rules?.biz_scale}
          >
            <Select placeholder="请选择业务规模" options={bizScaleOptions} disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item
            name="usability_desc"
            label={
              <div className="flex items-center gap-2">
                数据信息可用性说明
                <Tooltip title="说明最新数据的更新时间，如“每月1日23时后可用上月数据”">
                  <InfoCircleOutlined />
                </Tooltip>
              </div>
            }
            rules={rules?.usability_desc}
          >
            <Input placeholder="请输入可用性说明" disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="db_type" label="数据库或文件类型" rules={rules?.db_type}>
            <Select
              placeholder="请选择数据库或文件类型"
              options={dbTypeOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="db_name" label="所在库或服务器名称" rules={rules?.db_name}>
            <Input placeholder="请填写所在库或服务器名称" disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="table_name" label="所在表或文件路径名称" rules={rules?.table_name}>
            <Input placeholder="请填写所在表或文件路径名称" disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={itemSpan}>
          <Form.Item name="storage_type" label="数据资源存储形式" rules={rules?.storage_type}>
            <Select
              placeholder="请选择数据资源存储形式"
              options={storageTypeOptions}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

ApplyFormTech.displayName = 'ApplyFormTech';
