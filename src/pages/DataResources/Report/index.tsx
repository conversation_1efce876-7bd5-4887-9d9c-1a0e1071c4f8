import { ReactComponent as ReportIcon } from '@/assets/report_icon.svg';
import { SearchBox } from '@/components/SearchBox';
import { getList, ListData, ResponseData } from '@/services/dataResources/reportApi';
import { useModel } from '@umijs/max';
import { But<PERSON>, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { history } from 'umi';
import { ReportList } from './components/List';

function Report() {
  const { initialState } = useModel('@@initialState');

  const mockSearchTypeOptions = [
    { label: '表名', key: 'name' },
    { label: '主要数据信息', key: 'main_info' },
    { label: '来源系统/报表', key: 'source_name' },
    { label: '负责人', key: 'head_name' },
  ];
  // 0：我上报的，1:待我审批的
  const [type, setType] = useState('0');
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10 });
  const [listData, setListData] = useState<ListData['list']>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [searchType, setSearchType] = useState<string | number>('');
  const [filters, setFilters] = useState({});

  const getListData = async () => {
    setLoading(true);
    try {
      const response: ResponseData<ListData> = await getList({
        [searchType]: search,
        current: pagination.page,
        pageSize: pagination.pageSize,
        type: type,
        ...filters,
      });
      if (response.code !== 200001) return;
      setListData(response.data.list);
      setTotal(response.data.total);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getListData();
  }, [type, search, searchType, filters, pagination]);

  return (
    <Spin spinning={loading}>
      <div>
        <h1 className="flex gap-x-4 items-center justify-center">
          <ReportIcon height="40px" width="40px" />
          <span className="text-3xl font-medium">上报申请管理</span>
        </h1>
        <div className="mt-5">
          <SearchBox
            searchTypeOptions={mockSearchTypeOptions}
            addonInputBefore={() => {
              return <div className="w-[108px]"></div>;
            }}
            actions={() => {
              return (
                <div className="flex items-center">
                  <div className="space-x-2 flex-1">
                    <Button
                      type={type === '0' ? 'primary' : 'default'}
                      onClick={() => setType('0')}
                    >
                      我上报的
                    </Button>
                    {initialState?.currentUser?.roles[0] !== 'user' && (
                      <Button
                        onClick={() => setType('1')}
                        type={type === '1' ? 'primary' : 'default'}
                      >
                        待我审批
                      </Button>
                    )}
                  </div>
                  <div className="flex-shrink-0 flex-grow-0">
                    <Button
                      type="primary"
                      onClick={() => history.push('/dataResources/report/apply')}
                    >
                      数据上报
                    </Button>
                  </div>
                </div>
              );
            }}
            onSearchChange={(search, searchType) => {
              setPagination({
                ...pagination,
                page: 1,
              });
              setSearch(search);
              setSearchType(searchType);
            }}
            onFilterChange={(filtersObj, filtersArr) => {
              const filters = filtersArr.reduce((acc, cur) => {
                return {
                  ...acc,
                  [cur[0]]: cur[1][0],
                };
              }, {});
              setPagination({
                ...pagination,
                page: 1,
              });
              setFilters(filters);
            }}
          />
        </div>
        <div className="mt-5">
          <ReportList
            data={listData}
            total={total}
            type={type}
            refreshData={getListData}
            onPaginationChange={(page, pageSize) =>
              setPagination({
                page: page,
                pageSize: pageSize,
              })
            }
          />
        </div>
      </div>
    </Spin>
  );
}
export default Report;
