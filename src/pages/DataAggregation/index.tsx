import { ReactComponent as ReportIcon } from '@/assets/report_icon.svg';
import { GetDataAggregation } from '@/services/DataAggregation';
import { ProTable } from '@ant-design/pro-components';
import { <PERSON><PERSON>, ConfigProvider, Divider, Flex, message, Radio, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const DataAggregation: React.FC = () => {
  const [mainDepartment, setMainDepartment] = useState('');
  const [aggregationField, setAggregationField] = useState('');

  const [tableData, setTableData] = useState([]);

  const [loading, setLoading] = useState(false);

  const getTableData = async () => {
    setLoading(true);

    try {
      const res = await GetDataAggregation({ manage_dept: mainDepartment, area: aggregationField });

      if (res.code !== 200001) {
        message.error(res.message);
        setTableData([]);
        return;
      }

      setTableData(res.data.data.data);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getTableData();
  }, [mainDepartment, aggregationField]);

  const columns = [
    {
      title: '汇聚领域',
      dataIndex: 'area',
    },
    {
      title: '数据主管部门',
      dataIndex: 'manage_dept',
    },
    {
      title: '报表数量',
      dataIndex: 'table_count',
    },
    {
      title: '来源系统',
      dataIndex: 'source_sys',
    },
    {
      title: '更新频率',
      dataIndex: 'report_frequency',
    },
    {
      title: '最近上报时间',
      dataIndex: 'reported_at',
      render: (_dom: React.ReactNode, entity: any) => {
        return entity.reported_at ? dayjs(entity.reported_at).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '最近更新时间',
      dataIndex: 'updated_at',
      render: (_dom: React.ReactNode, entity: any) => {
        return entity.updated_at ? dayjs(entity.updated_at).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_dom: React.ReactNode, entity: any) => (
        <Button
          type="link"
          href={`/dataResources/dataAggregation/collectionStatus?area=${entity.area}`}
          rel="noopener noreferrer"
        >
          查看采集情况
        </Button>
      ),
    },
  ];

  return (
    <>
      <h1 className="flex gap-x-4 items-center justify-center my-16">
        <ReportIcon height="40px" width="40px" />
        <span className="text-3xl font-medium">数据汇聚</span>
      </h1>

      <div className="border-[#F0F0F0] rounded-3xl p-6 border">
        <Flex align="center" gap={48} className="mb-8">
          <div className="font-medium">数据主管部门:</div>
          <Radio.Group
            defaultValue=""
            buttonStyle="solid"
            value={mainDepartment}
            onChange={(e) => setMainDepartment(e.target.value)}
            disabled={loading}
          >
            <Radio.Button value="">全部</Radio.Button>
            <Radio.Button value="财务金融中心">财务金融中心</Radio.Button>
            <Radio.Button value="运营主管部门">运营主管中心</Radio.Button>
            <Radio.Button value="风法中心">风法中心</Radio.Button>
            {/* <Radio.Button value="战投部门">战投部门</Radio.Button> */}
            {/* <Radio.Button value="科信中心">科信中心</Radio.Button> */}
          </Radio.Group>
        </Flex>
        <Flex align="center" gap={48}>
          <div className="font-medium">汇聚领域:</div>
          <Select
            value={aggregationField}
            onChange={(value) => setAggregationField(value)}
            options={[
              { value: '', label: '不限' },
              { value: '财务金融', label: '财务金融' },
              { value: '运营', label: '运营' },
              { value: '采购', label: '采购' },
              { value: '合同', label: '合同' },
            ]}
            defaultValue=""
            className="w-96"
            disabled={loading}
          ></Select>
        </Flex>
        <Divider dashed />
        <ConfigProvider
          theme={{
            components: {
              Table: {
                headerBg: '#D9E0F2',
              },
            },
          }}
        >
          <ProTable
            options={false}
            search={false}
            columns={columns}
            dataSource={tableData}
            loading={loading}
            scroll={{ x: 'max-content' }}
            rowKey="index"
            pagination={false}
          ></ProTable>
        </ConfigProvider>
      </div>
    </>
  );
};

export default DataAggregation;
