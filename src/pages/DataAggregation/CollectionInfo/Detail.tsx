import { GetCollectionDetail } from '@/services/DataAggregation';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ConfigProvider, message, Modal, Table, Tooltip } from 'antd';
import { useEffect, useState } from 'react';

const CollectionDetail = ({
  isOpen,
  setIsOpen,
  company_code,
  company_code_col,
  table_name_en,
  table_name,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  company_code: string | undefined;
  company_code_col: string | undefined;
  table_name_en: string | undefined;
  table_name: string | undefined;
}) => {
  const [detailData, setDetailData] = useState([]);
  const [detailLoading, setDetailLoading] = useState(false);

  const [columns, setColumns] = useState([]);

  const getDetailData = async () => {
    setDetailLoading(true);

    try {
      const res = await GetCollectionDetail({
        company_code,
        company_code_col,
        table_name_en,
        use_total_num: 1,
      });

      if (res.code !== 200001) {
        setDetailData([]);
        return;
      }
      setColumns(res.data.data.colMap || []);

      setDetailData(res.data.data.data || []);
    } catch (error) {
      message.error(JSON.stringify(error));
    } finally {
      setDetailLoading(false);
    }
  };

  const extractColumns = (columnsMap: Record<string, any>) => {
    if (!columnsMap || Object.keys(columnsMap).length === 0) return [];

    const allColumns = Object.keys(columnsMap);

    const columns = allColumns.map((col) => ({
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <span>{columnsMap[col].column_name}</span>
          <Tooltip title={columnsMap[col].comments || '暂无说明'}>
            <QuestionCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
          </Tooltip>
        </div>
      ),
      dataIndex: col,
      key: col,
      render: (text: string) => {
        if (typeof text !== 'string') return text;
        const isLong = text.length > 30;
        const displayText = isLong ? text.slice(0, 30) + '...' : text;
        return (
          <Tooltip
            title={isLong ? text : undefined}
            overlayStyle={{ maxWidth: '80vw' }}
            overlayInnerStyle={{ maxHeight: 300, overflowY: 'auto' }}
          >
            <span>{displayText}</span>
          </Tooltip>
        );
      },
    }));

    return columns;
  };

  useEffect(() => {
    if (!isOpen || !company_code || !company_code_col || !table_name_en || !table_name) {
      return;
    }
    getDetailData();
  }, [isOpen, company_code, company_code_col, table_name_en, table_name]);

  return (
    <Modal
      open={isOpen}
      onCancel={() => setIsOpen(false)}
      title={table_name}
      footer={null}
      width={1300}
    >
      <ConfigProvider
        theme={{
          components: {
            Table: {
              headerBg: '#D9E0F2',
            },
          },
        }}
      >
        <Table
          className="max-h-[75vh]"
          sticky
          columns={extractColumns(columns)}
          dataSource={detailData}
          loading={detailLoading}
          pagination={false}
          scroll={{ x: 'max-content', y: '70vh' }}
        />
      </ConfigProvider>
    </Modal>
  );
};

export default CollectionDetail;
