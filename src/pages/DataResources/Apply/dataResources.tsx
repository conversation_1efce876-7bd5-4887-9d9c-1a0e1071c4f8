import { SearchBox } from '@/components/SearchBox';
import {
  getResourceUseListData,
  ResourceUseListData,
  ResourceUseResponse,
} from '@/services/dataResources/applyApi';
import { useModel } from '@umijs/max';
import { But<PERSON>, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { ApplyList } from './components/List';

function DataResources() {
  const { initialState } = useModel('@@initialState');

  const searchTypeOptions = [
    { label: '表名', key: 'name' },
    { label: '主要数据信息', key: 'main_info' },
    { label: '来源系统/报表', key: 'source_name' },
    { label: '负责人', key: 'head_name' },
  ];

  const [filters, setFilters] = useState({});
  const [listData, setListData] = useState<ResourceUseListData['list']>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10 });
  const [search, setSearch] = useState('');
  const [searchType, setSearchType] = useState<string | number>('');
  const [total, setTotal] = useState(0);
  const [type, setType] = useState('0');

  const getListData = async () => {
    setLoading(true);
    try {
      const response: ResourceUseResponse<ResourceUseListData> = await getResourceUseListData({
        [searchType]: search,
        current: pagination.page,
        pageSize: pagination.pageSize,
        type,
        ...filters,
      });

      if (response.code !== 200001) return;
      setListData(response.data.list);
      setTotal(response.data.total);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getListData();
  }, [filters, pagination, search, searchType, type]);

  return (
    <Spin spinning={loading}>
      <div>
        <div className="mt-5">
          <SearchBox
            searchTypeOptions={searchTypeOptions}
            addonInputBefore={() => {
              return <div className="w-[108px]"></div>;
            }}
            actions={() => {
              return (
                <div className="flex items-center">
                  <div className="space-x-2 flex-1">
                    <Button
                      onClick={() => setType('0')}
                      type={type === '0' ? 'primary' : 'default'}
                    >
                      我申请的
                    </Button>
                    {initialState?.currentUser?.roles[0] !== 'user' && (
                      <Button
                        onClick={() => setType('1')}
                        type={type === '1' ? 'primary' : 'default'}
                      >
                        待我审批
                      </Button>
                    )}
                  </div>
                </div>
              );
            }}
            onSearchChange={(search, searchType) => {
              setPagination({ ...pagination, page: 1 });
              setSearch(search);
              setSearchType(searchType);
            }}
            onFilterChange={(filtersObj, filtersArr) => {
              const filters = filtersArr.reduce((acc, cur) => {
                return {
                  ...acc,
                  [cur[0]]: cur[1][0],
                };
              }, {});
              setFilters(filters);
              setPagination({ ...pagination, page: 1 });
            }}
          />
        </div>
        <div className="mt-5">
          <ApplyList
            data={listData}
            total={total}
            // refreshData={getListData}
            type={type}
            onPaginationChange={(page, pageSize) => {
              setPagination({ page, pageSize });
            }}
          />
        </div>
      </div>
    </Spin>
  );
}
export default DataResources;
