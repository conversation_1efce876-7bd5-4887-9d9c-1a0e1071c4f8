import { getOrgsTree } from '@/services/dataResources/orgsApi';
import { GetUpdateList } from '@/services/dataResources/upDateList';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { ConfigProvider } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

type GithubIssueItem = any;

export default function UpdatesList() {
  const actionRef = useRef<ActionType>();
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]); // 添加 expandedRowKeys 状态
  //公司列表
  const [fieldProps, setCompanyList] = useState<any>([]);
  //page
  const [currentPage, setCurrentPage] = React.useState<any>(1);
  const [pageSize, setPageSize] = React.useState<number>(10);
  //listData
  const [listData, setListData] = React.useState<any>([]);

  const columns: ProColumns<GithubIssueItem>[] = [
    {
      title: '数据编号',
      dataIndex: 'num',
      ellipsis: true,
      key: 'num',
      width: 140,
      hideInSearch: true,
    },
    {
      title: '数据资源中文名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 200,
    },
    {
      title: '归口部门/公司',
      dataIndex: 'owner_dept',
      ellipsis: true,
      key: 'owner_dept',
      width: 200,
      valueType: 'treeSelect',
      fieldProps: {
        treeData: fieldProps,
        fieldNames: {
          label: 'name',
          value: 'id',
          children: 'children',
        },
      },
    },
    {
      title: '更新操作人',
      dataIndex: 'operator',
      ellipsis: true,
      key: 'operator',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '更新内容',
      dataIndex: 'update_contents',
      ellipsis: true,
      key: 'update_contents',
      width: 300,
      hideInSearch: true,
      render: (text, record) => {
        const isExpanded = expandedRowKeys.includes(record.id); // 检查当前行是否展开
        const handleToggleExpand = (e: React.MouseEvent) => {
          e.stopPropagation(); // 阻止事件冒泡
          if (isExpanded) {
            setExpandedRowKeys(expandedRowKeys.filter((k) => k !== record.id)); // 收起
          } else {
            setExpandedRowKeys([...expandedRowKeys, record.id]); // 展开
          }
        };
        return (
          <div className="flex items-center gap-1">
            <div className="flex-1 truncate">
              {record.update_contents?.map((item: any, index: number) => {
                return (
                  <div key={index} className="truncate">
                    <span>{item?.field} : </span>
                    <span>{item?.new}</span>
                  </div>
                );
              })}
            </div>
            <div>
              <a className="text-[#002FA5] hover:underline" onClick={handleToggleExpand}>
                {isExpanded ? '收起' : '查看更多'}
              </a>
            </div>
          </div>
        );
      },
    },
    {
      title: '更新时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record) => [
        <a
          key="detail"
          className="hover:underline text-[#002FA5]"
          onClick={() => history.push(`/dataResources/list/detail/${record?.resource_id}`)}
        >
          查看详情
        </a>,
      ],
    },
  ];

  //获取公司列表
  const getCompanyId = async () => {
    const response = await getOrgsTree();
    if (response.code === 200001) {
      setCompanyList(response.data);
    }
  };

  useEffect(() => {
    getCompanyId();
  }, []);

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBg: '#D9E0F2',
          },
        },
      }}
    >
      <div className="border-[#F0F0F0] rounded-[20px] p-[24px] border-[1px]">
        <ProTable<GithubIssueItem>
          columns={columns}
          actionRef={actionRef}
          cardBordered
          className="zygx-table"
          request={async (params) => {
            if (params.owner_dept) {
              params.owner_dept_id = params.owner_dept;
              delete params.owner_dept;
            }
            const res = await GetUpdateList(params);
            setListData(res.data);
            return {
              data: res.data.list,
              success: true,
              total: res.data.total,
            };
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              option: { fixed: 'right', disable: true },
            },
            onChange(value) {
              console.log('value: ', value);
            },
          }}
          rowKey="id"
          search={{
            labelWidth: 'auto',
          }}
          options={false}
          expandable={{
            expandedRowRender: (record) => (
              <p style={{ margin: 0 }} className="pl-[20px]">
                <div className="text-[#333] font-medium">更新内容 :</div>
                {record.update_contents?.map((item: any, index: number) => {
                  return (
                    <div key={index} className="truncate">
                      <span>{item?.field} : </span>
                      <span>{item?.new}</span>
                    </div>
                  );
                })}
                <div className="absolute right-[16px] bottom-[16px]">
                  <a
                    className="text-[#002FA5] hover:underline"
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡
                      setExpandedRowKeys(expandedRowKeys.filter((k) => k !== record.id)); // 收起
                    }}
                  >
                    收起
                  </a>
                </div>
              </p>
            ),
            showExpandColumn: false,
            expandedRowKeys: expandedRowKeys, // 传递 expandedRowKeys
            onExpandedRowsChange: (keys) => {
              setExpandedRowKeys(keys); // 更新 expandedRowKeys 状态
            },
          }}
          pagination={{
            total: listData.total,
            onChange: (page) => {
              setCurrentPage(page);
            },
            onShowSizeChange: (current, pageSize) => {
              setPageSize(pageSize);
            },
            pageSize,
            current: currentPage,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => (
              <>
                共 {listData.total} 条 本页显示第 {range[0]} ~ {range[1]} 条
              </>
            ),
          }}
          dateFormatter="string"
          toolBarRender={() => []}
        />
      </div>
    </ConfigProvider>
  );
}
