import { ReactNode } from 'react';
import { DataListContent } from './Content';
import { DataListDescription } from './Description';
import { DataListFooter } from './Footer';
import { DataListHeader } from './Header';
import { DataListItem } from './Item';
import { DataListPagination } from './Pagination';

interface DataListProps {
  children?: ReactNode;
}
export const DataList = ({ children }: DataListProps) => {
  return <div className="space-y-4">{children}</div>;
};

DataList.displayName = 'DataList';

DataList.Item = DataListItem;
DataList.Header = DataListHeader;
DataList.Content = DataListContent;
DataList.Description = DataListDescription;
DataList.Footer = DataListFooter;
DataList.Pagination = DataListPagination;
