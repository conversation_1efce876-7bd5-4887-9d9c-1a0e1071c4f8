import { UcLogin } from '@/services/Login';
import { login_uri, saveToken } from '@/utils/loginUntils';
import { Button, message, Spin } from 'antd';
import React, { useEffect, useState } from 'react';

import styles from './index.less';

const UCLogin: React.FC = () => {
  const query = new URLSearchParams(location.search);
  const code = query.get('code');
  const [loading, setLoading] = useState(false);

  const redirectToUC = () => {
    window.location.href = login_uri;
  };

  // 根据 code 请求 token 数据
  const handleGetUserToken = async () => {
    setLoading(true);
    try {
      const loginParams: any = { code };
      const res = await UcLogin(loginParams);
      if (res?.code === 200001) {
        saveToken(res?.data?.token);
        message.success('登录成功！');
        window.location.href = '/';
        return;
      }
      message.error('登录失败，请重试！');
      redirectToUC();
    } catch (error) {
      message.error(JSON.stringify(error));
      redirectToUC();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (code) {
      handleGetUserToken();
    } else {
      redirectToUC();
    }
  }, [code]);

  return (
    <Spin spinning={loading} tip="登录中..." size="large">
      <div className={styles.container}>
        <div className={styles.login_bg} />
        <div className={styles.content}>
          <div className={styles.logo_txt} />
          <div className={styles.login_btn}>
            <div className={styles.welcome}>欢迎，登录</div>
            <div className={styles.title}>保利集团大数据管理平台</div>
            <div>
              <Button
                block
                type="primary"
                size="large"
                onClick={redirectToUC}
                style={{
                  width: '300px',
                  color: '#fff',
                  backgroundColor: '#365bc9',
                  borderColor: '#365bc9',
                }}
              >
                用户中心登录
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default UCLogin;
