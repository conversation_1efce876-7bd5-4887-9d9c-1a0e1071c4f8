import { GetMajorDataDetail } from '@/services/dataResources/listApi';
import { QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Button, Input, Modal, Space, Spin, Table, Tooltip } from 'antd';
import type { ColumnType } from 'antd/es/table';
import type { FilterDropdownProps } from 'antd/es/table/interface';
import React, { useEffect, useRef, useState } from 'react';

const COLUMN_DESCRIPTIONS: Record<string, Record<string, string>> = {
  '组织架构-法人组织': {
    comcode: '企业编码',
    name: '企业法定全称。为所属中央企业集团全国统一社会信用代码证书上的机构名称，非相关财务公司或子公司的单位名称。采用《法人和其他组织统一社会信用代码编码规则》（GB 32100）最新标准对应的注册名称。境外：所在国家/地区注册登记书的组织名称。',
    code: '社会统一信用代码。1、内部供应商境内：为对方单位的18位全国统一社会信用代码，采用《法人和其他组织统一社会信用代码编码规则》（GB 32100）最新标准2、内部供应商境外，使用财务23年统一下发的编码规则。',
    establishment_date: '营业执照颁发日期',
    orgtiers:
      '集团型企业的管理层级划分。1-一级/2-一级总部/3-二级/4-二级本部/5-三级/6-四级/7-五级及以下',
    country_name: '国家地区',
    kind: '国标企业类型代码。企业类别包括国有全资, 国有参股, 国有控股, 国有独资, 其他',
    level: '企业法人层级',
    province: '省份',
    citycode: '城市',
    entscale: '企业规模。1-大型，2-中型，3-小型，4-微型',
    islisted: '是否上市',
    industry: '企业所在行业',
    regcapital: '企业实缴或认缴的资本总额',
    companystatus: '企业存续状态',
    ismgmtorg: '是否管理组织',
    parententname: '管理线汇报上级组织的名称',
    parententcode: '管理线汇报上级组织的信用代码',
    islegalentity: '是否法人组织',
    parentlegalentcode: '上级法人组织统一信用代码',
    parentlegalentname: '上级法人组织名称',
  },
  '组织架构-部门': {
    deptcode: '部门编码',
    deptnamezh: '部门名称(ZH)',
    belongorg: '所属组织',
    parentdept: '上级部门',
    depttype: '部门类型。01普通部门，02虚拟部门',
    deptestdate: '部门成立时间',
    status: '启用状态',
    displayorder: '显示顺序',
    director: '负责人',
    divisionleader: '分管领导',
  },
  员工: {
    id: '员工编码',
    full_name: '员工姓名',
    gender: '性别',
    mobile: '手机号',
    company_name: '所属公司名称',
    belongcompany_id: '所属公司的编码',
    department_name: '所属部门名称',
    parentdept_id: '所属部门的编码',
    status: '员工状态',
    main_duties: '主要职务',
  },
  银行账户: {
    comcode:
      '所属集团编码。为所属中央企业集团的18位全国统一社会信用代码，非相关财务公司或子公司的统一社会信用代码。',
    comname:
      '所属集团名称。为所属中央企业集团全国统一社会信用代码证书上的机构名称，非相关财务公司或子公司的单位名称。',
    openunitcode: '开户单位编码。与《保利集团会计核算及报账系统》中的组织编码保持一致。',
    clt_name: `"开户单位名称。为银行账户开户单位的全称。
境内：为开户单位的全国统一社会信用代码证书上的机构名称，采用《法人和其他组织统一社会信用代码编码规则》（GB 32100）最新标准。
境外：境外企业注册名称"`,
    comlevel: `开户单位企业级次。L00：本级
L01：一级
L02：二级
L03：三级
以此类推，例如四级为L04,五级为L05`,
    account_name: `单位户名。账户户名为银行账户的名称。
境内：为开户单位的全国统一社会信用代码证书上的机构名称，采用《法人和其他组织统一社会信用代码编码规则》（GB 32100）最新标准。
境外：境外企业注册名称`,
    account_no: `"单位账号。
账号报送范围为企业在银行等金融机构开设的外部账户。"`,
    subcompany: `是否为内部单位账户。T00：非内部单位账户；
T01：内部单位账户；
注：内部企业指集团内部存款账户，如财务公司账户。`,
    ban_cnaps_name: '开户银行名称',
    bankcodenumber: '开户银行等金融机构联行号',
    country: `开户银行所在国所在地区。账户所在地区表示开设的银行账户所属地区名称。
境内：默认中国
境外：采用《世界各国和地区名称代码》（GB/T2659）最新版中文简称`,
    crncycode: `币种。账户所在地区表示开设的银行账户所属地区名称。
境内：默认中国
境外：采用《世界各国和地区名称代码》（GB/T2659）最新版中文简称`,
    accounttype: `账户用途。01：基本户
02：临时户
03：一般户
04：专用户
05：虚拟户
06：多币种账户`,
    direct_type: '开通银企直连标识。00：未开通；01：已开通',
    direct_time: '开通银企直联时间',
    onlinetype: `集中资金账户标识。00：非监控；（是否归集受限为 是）
01：可监控
02：可归集；
其中：
可归集：代表采用资金池/结算中心/在财务公司存款方式实现下属单位的钱可以集中起来
可监控：集团可通过银企直连/网银信息自动抓取/swift代理监控下属单位的资金，但不能进行操作`,
    accountsta: `银行账户存续状态。00：存续；
01：注销`,
    account_startdt: '开户时间',
    account_enddt: '销户时间',
  },
  会计科目: {
    acctcode: '科目编码',
    item1: '一级科目名称',
    item2: '二级科目名称',
    item3: '三级科目名称',
    item4: '四级科目名称',
    item5: '五级科目名称',
    item6: '六级科目名称',
    item7: '七级科目名称',
    fullname: '科目全称',
    appsegment: '适用板块',
    rptitem: '报表项目',
    baldirection: '科目余额增减方向',
    bankacct:
      '指企业在银行开立的各项银行账户，如按照账户性质，分为基本存款账户、一般存款账户、各项专用账户等。',
    supplier:
      '指与中国保利集团管辖范围内的企业发生经济往来的人或组织机构，组织机构指国家机关、企业和事业单位、社会团体以及其他依法设立的组织。',
    deptarchv: '指在中国保利集团及下属单位设立的组织机构部门。',
    staffarchv:
      '指在中国保利集团及下属单位实际从事生产经营活动的全部人员、由于各种原因已经离开本单位的工作岗位但仍与本单位保留劳动关系的职工、已办理离退休手续的职工等。',
    contract:
      '指当事人或当事双方之间设立、变更、终止民事关系的协议。合同的特征为：(1)合同是平等的民事主体之间的协议；(2)合同是一种民事法律行为；(3)合同以设立、变更或终止民事权利义务关系为目的；(4)合同是双方或多方的民事法律行为。依法成立的合同，受法律保护。由各业务单位自行设置。',
    materialcat: '对原料、辅料等与产品相关的所有物品的基本分类。',
    welfareproj: '对各项福利费内容的细分。',
    project:
      '指具有独特的过程，有开始和结束日期，由一系列相互协调和受控的活动组成。过程的实施是为了达到规定的目标，包括满足时间、费用和资源等约束条件。项目能承载结构化、多维度的信息。由各业务单位自行设置。',
    banktype:
      '即银行行别，指各具体金融机构的类别，金融机构的类别包括银行、证券公司、保险公司、信托投资公司和基金管理公司等。',
    bankarchv:
      '指从事金融业有关的金融中介机构，为金融体系的一部分，金融业包括银行、证券、保险、信托、基金等行业。',
    finasset:
      '金融资产是指一切代表未来收益或资产合法要求权的凭证，亦称金融工具或证券。是指单位或个人拥有的以价值形态存在的资产，是一种索取实物资产的权利。',
    finliability:
      '金融负债，指基于下列合同义务的负债：(1)向另一个企业交付现金或另一金融资产合同义务；(2)在潜在不利的条件下，与另一企业交换金融工具的合同义务《国际会计准则》第39号在涉及金融负债的确认与计量时指出“初始确认”当且仅当成为金融工具合同条款的一方时，企业应在其资产负债表上确认金融资产或金融负债。终止确认“当且仅当金融负债消除时，企业才能从资产负债表上将其剔除”。初始计量“当金融资产和金融负债初始确认时，企业应以其成本进行计量。就金融负债而言，成本指收到的对价的公允价值。交易费用应计入各金融资产和金融负债成本。”后续计量“初始确认后，企业应以摊余成本计量各种金融负债”。',
    restatus:
      '指与房地产开发行业相关的用于归集开发产品及成本、确认收入时需要登记维护的房地产项目及业态等基本信息。',
    accrualmethod: '指计算和提取。按规定的比率与规定的基数相乘计算提取，用于核算减值的计提方式。',
    impairadjmethod: '核算减值准备具体增加、减少原因。',
    debtfincontract: '核算各类融资产品。',
    cfitem: '指投资项目在其整个寿命期内所发生的现金流出和现金流入的全部资金收付数量。',
    respcenter:
      '从属于某业务单元的，承担一定经济责任并享有一定权利的内部业绩考核核算单元。包括成本中心、利润中心和投资中心。责任中心可以由部门或人员构成，也可由前述两者依据确定关系组合构成。由各业务单位自行设置。',
    taxrate: '指税收的款项，是课税对象的具体项目。',
    materialarchv: '指基于物料基本分类下，该产品的型号、计量单位、税类等等基本信息。',
  },
  供应商: {
    vendorno: '唯一标识供应商的编码体系',
    vendorname:
      '供应商的法定注册名称，在工商或行政管理部门登记注册的企业名称，个人为有效证件是记载名',
    vendorcategory: '按业务关系和产品类型划分的供应商类别',
    vendortype: `按所有制形式和行业属性划分的企业类别。
K01：中央企业；
K02：地方国有企业；
K03：中央部委；
K04：地方政府；
K05：民营企业；
K06: 其他；
K07: 事业单位；
K08: 个体工商户
K09: 政府平台公司`,
    vendorcode: `统一社会信用代码。
1、内部供应商境内：为对方单位的18位全国统一社会信用代码
2、内部供应商境外，遵循财务23年统一下发的编码规则的代码
3、外部供应商境内：为对方单位的18位全国统一社会信用代码
4、外部供应商境外：遵循集团招采平台中供应商统一社会信用代码管理要求的代码
5、外部供应商个人：中国国籍为身份证号码，外籍为护照号
6、涉密或者涉军等企事业单位，查询确无信用代码的，信用代码为汉字"无"`,
    subcompany: `用于识别是否为集团内部单位。T00：非内部企业;
T01：内部企业`,
    comname: '所在企业集团名称',
    areacl: `国家地区。供应商所在经营地的国家和地区的信息。
境内：默认中国`,
    vendoraddresscode: `企业地址编码。供应商所在经营地省市代码信息，
境内：采用《中华人民共和国行政区划代码（GB2260）最新版6位代码
境外：采用《世界各国和地区名称代码》（GB/T2659）最新版地区中文简称`,
    creatdate: '供应商入库日期。首次注册入招采企业库的时间',
    entscale: '企业规模。1-大型，2-中型，3-小型，4-微型，5-其他',
    regcapital: '工商营业执照上的注册资金',
    establishdate: '成立日期',
    vendorindustry:
      '企业主营业务的国民经济行业分类。《国民经济行业分类代码》（GB/T4754）的大类代码',
    legalperson: '企业法人',
    vendoraddress: '营业执照登记的法律住所',
    lol: '企业经营状态',
    blacklist_flag: '供应商是否被列入黑名单。Y/N，Y代表是，N代表否',
    cr: '供应商信用评级，揭示受评对象违约风险的大小',
    deregistration_date: '注销时间',
    tradingcom: '往来单位',
  },
  客户: {
    customerno: '唯一标识客户的编码体系',
    customername: '客户的法定注册名称',
    customercategory: '按业务关系和产品类型划分的客户类别。',
    customertype: `按所有制形式和行业属性划分的企业类别。K01：中央企业；
K02：地方国有企业；
K03：中央部委；
K04：地方政府；
K05：民营企业；
K06: 其他；
K07: 事业单位；
K08: 个体工商户
K09：政府平台公司`,
    customercode: `统一社会信用代码。
1、内部客户境内：为对方单位的18位全国统一社会信用代码
2、内部客户境外，遵循财务23年统一下发的编码规则的代码

3、外部客户境内：为对方单位的18位全国统一社会信用代码
4、外部客户境外：遵循集团招采平台中客户统一社会信用代码管理要求的代码
5、外部客户个人：中国国籍为身份证号码，外籍为护照号。
6、涉密或者涉军等企事业单位，查询确无信用代码的，信用代码为汉字"无"`,
    subcompany: `用于识别是否为集团内部单位。T00：非内部企业;
  T01：内部企业`,
    comname: '所在企业集团名称',
    areacl: `国家地区。客户所在经营地的国家和地区的信息。
  境内：默认中国`,
    customeraddresscode: `企业地址编码。客户所在经营地省市代码信息，
  境内：采用《中华人民共和国行政区划代码（GB2260）最新版6位代码
  境外：采用《世界各国和地区名称代码》（GB/T2659）最新版地区中文简称`,
    creatdate: '客户创建时间。首次注册入招采企业库的时间',
    entscale: '企业规模。1-大型，2-中型，3-小型，4-微型，5-其他',
    regcapital: '工商营业执照上的注册资金',
    establishdate: '成立日期',
    customerindustry:
      '企业主营业务的国民经济行业分类。《国民经济行业分类代码》（GB/T4754）的大类代码',
    legalperson: '企业法人',
    customeraddress: '营业执照登记的法律住所',
    lol: '企业经营状态',
    blacklist_flag: '客户是否被列入黑名单。Y/N，Y代表是，N代表否',
    cr: '客户信用评级，揭示受评对象违约风险的大小',
    deregistration_date: '注销时间',
  },
};

const createColumnWithTooltip = (
  title: string,
  dataIndex: string,
  key: string,
  dataType: string,
) => ({
  title: (
    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
      <span>{title}</span>
      <Tooltip title={COLUMN_DESCRIPTIONS[dataType]?.[dataIndex] || '暂无说明'}>
        <QuestionCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
      </Tooltip>
    </div>
  ),
  dataIndex,
  key,
  width: Math.max(title.length * 14 + 30, 120),
  ellipsis: {
    showTitle: false,
  },
});

const DEPARTMENT_COLUMNS = [
  createColumnWithTooltip('部门编码', 'deptcode', 'deptcode', '组织架构-部门'),
  createColumnWithTooltip('部门名称(ZH)', 'deptnamezh', 'deptnamezh', '组织架构-部门'),
  createColumnWithTooltip('所属组织', 'belongorg', 'belongorg', '组织架构-部门'),
  createColumnWithTooltip('上级部门', 'parentdept', 'parentdept', '组织架构-部门'),
  createColumnWithTooltip('部门类型', 'depttype', 'depttype', '组织架构-部门'),
  createColumnWithTooltip('部门成立时间', 'deptestdate', 'deptestdate', '组织架构-部门'),
  createColumnWithTooltip('启用状态', 'status', 'status', '组织架构-部门'),
  createColumnWithTooltip('显示顺序', 'displayorder', 'displayorder', '组织架构-部门'),
  createColumnWithTooltip('负责人', 'director', 'director', '组织架构-部门'),
  createColumnWithTooltip('分管领导', 'divisionleader', 'divisionleader', '组织架构-部门'),
];

const SUPPLIER_COLUMNS = [
  createColumnWithTooltip('供应商编码', 'vendorno', 'vendorno', '供应商'),
  createColumnWithTooltip('供应商名称', 'vendorname', 'vendorname', '供应商'),
  createColumnWithTooltip('供应商基本分类', 'vendorcategory', 'vendorcategory', '供应商'),
  createColumnWithTooltip('企业类型', 'vendortype', 'vendortype', '供应商'),
  createColumnWithTooltip('统一社会信用代码', 'vendorcode', 'vendorcode', '供应商'),
  createColumnWithTooltip('集团内外部标识', 'subcompany', 'subcompany', '供应商'),
  createColumnWithTooltip('所在企业集团', 'comname', 'comname', '供应商'),
  createColumnWithTooltip('国家地区', 'areacl', 'areacl', '供应商'),
  createColumnWithTooltip('企业地址编码', 'vendoraddresscode', 'vendoraddresscode', '供应商'),
  createColumnWithTooltip('供应商入库日期', 'creatdate', 'creatdate', '供应商'),
  createColumnWithTooltip('企业规模', 'entscale', 'entscale', '供应商'),
  createColumnWithTooltip('注册资金（万元）', 'regcapital', 'regcapital', '供应商'),
  createColumnWithTooltip('成立日期', 'establishdate', 'establishdate', '供应商'),
  createColumnWithTooltip('所属行业', 'vendorindustry', 'vendorindustry', '供应商'),
  createColumnWithTooltip('法定代表人', 'legalperson', 'legalperson', '供应商'),
  createColumnWithTooltip('企业地址', 'vendoraddress', 'vendoraddress', '供应商'),
  createColumnWithTooltip('企业状态', 'lol', 'lol', '供应商'),
  createColumnWithTooltip('黑名单标志', 'blacklist_flag', 'blacklist_flag', '供应商'),
  createColumnWithTooltip('信用评级', 'cr', 'cr', '供应商'),
  createColumnWithTooltip('注销时间', 'deregistration_date', 'deregistration_date', '供应商'),
  createColumnWithTooltip('往来单位', 'tradingcom', 'tradingcom', '供应商'),
];

const CUSTOMER_COLUMNS = [
  createColumnWithTooltip('客户编码', 'customerno', 'customerno', '客户'),
  createColumnWithTooltip('客户名称', 'customername', 'customername', '客户'),
  createColumnWithTooltip('客户基本分类', 'customercategory', 'customercategory', '客户'),
  createColumnWithTooltip('企业类型', 'customertype', 'customertype', '客户'),
  createColumnWithTooltip('统一社会信用代码', 'customercode', 'customercode', '客户'),
  createColumnWithTooltip('集团内外部标识', 'subcompany', 'subcompany', '客户'),
  createColumnWithTooltip('所在企业集团', 'comname', 'comname', '客户'),
  createColumnWithTooltip('国家地区', 'areacl', 'areacl', '客户'),
  createColumnWithTooltip('企业地址编码', 'customeraddresscode', 'customeraddresscode', '客户'),
  createColumnWithTooltip('客户入库日期', 'creatdate', 'creatdate', '客户'),
  createColumnWithTooltip('企业规模', 'entscale', 'entscale', '客户'),
  createColumnWithTooltip('注册资金（万元）', 'regcapital', 'regcapital', '客户'),
  createColumnWithTooltip('成立日期', 'establishdate', 'establishdate', '客户'),
  createColumnWithTooltip('所属行业', 'customerindustry', 'customerindustry', '客户'),
  createColumnWithTooltip('法定代表人', 'legalperson', 'legalperson', '客户'),
  createColumnWithTooltip('企业地址', 'customeraddress', 'customeraddress', '客户'),
  createColumnWithTooltip('企业状态', 'lol', 'lol', '客户'),
  createColumnWithTooltip('黑名单标志', 'blacklist_flag', 'blacklist_flag', '客户'),
  createColumnWithTooltip('信用评级', 'cr', 'cr', '客户'),
  createColumnWithTooltip('注销时间', 'deregistration_date', 'deregistration_date', '客户'),
];

const ACCOUNTS_COLUMNS = [
  createColumnWithTooltip('科目编码', 'acctcode', 'acctcode', '会计科目'),
  createColumnWithTooltip('一级科目名称', 'item1', 'item1', '会计科目'),
  createColumnWithTooltip('二级科目名称', 'item2', 'item2', '会计科目'),
  createColumnWithTooltip('三级科目名称', 'item3', 'item3', '会计科目'),
  createColumnWithTooltip('四级科目名称', 'item4', 'item4', '会计科目'),
  createColumnWithTooltip('五级科目名称', 'item5', 'item5', '会计科目'),
  createColumnWithTooltip('六级科目名称', 'item6', 'item6', '会计科目'),
  createColumnWithTooltip('七级科目名称', 'item7', 'item7', '会计科目'),
  createColumnWithTooltip('科目全称', 'fullname', 'fullname', '会计科目'),
  createColumnWithTooltip('适用板块', 'appsegment', 'appsegment', '会计科目'),
  createColumnWithTooltip('报表项目', 'rptitem', 'rptitem', '会计科目'),
  createColumnWithTooltip('余额方向', 'baldirection', 'baldirection', '会计科目'),
  createColumnWithTooltip('银行账户', 'bankacct', 'bankacct', '会计科目'),
  createColumnWithTooltip('供应商', 'supplier', 'supplier', '会计科目'),
  createColumnWithTooltip('部门档案', 'deptarchv', 'deptarchv', '会计科目'),
  createColumnWithTooltip('人员档案', 'staffarchv', 'staffarchv', '会计科目'),
  createColumnWithTooltip('合同', 'contract', 'contract', '会计科目'),
  createColumnWithTooltip('MaterialCat', 'materialcat', 'materialcat', '会计科目'),
  createColumnWithTooltip('福利费项目', 'welfareproj', 'welfareproj', '会计科目'),
  createColumnWithTooltip('项目', 'project', 'project', '会计科目'),
  createColumnWithTooltip('银行类别', 'banktype', 'banktype', '会计科目'),
  createColumnWithTooltip('银行档案', 'bankarchv', 'bankarchv', '会计科目'),
  createColumnWithTooltip('金融资产', 'finasset', 'finasset', '会计科目'),
  createColumnWithTooltip('金融负债', 'finliability', 'finliability', '会计科目'),
  createColumnWithTooltip('地产业态', 'restatus', 'restatus', '会计科目'),
  createColumnWithTooltip('计提方式', 'accrualmethod', 'accrualmethod', '会计科目'),
  createColumnWithTooltip('减值准备增减方式', 'impairadjmethod', 'impairadjmethod', '会计科目'),
  createColumnWithTooltip('债权类融资合同', 'debtfincontract', 'debtfincontract', '会计科目'),
  createColumnWithTooltip('现金流量项目', 'cfitem', 'cfitem', '会计科目'),
  createColumnWithTooltip('责任中心', 'respcenter', 'respcenter', '会计科目'),
  createColumnWithTooltip('税率', 'taxrate', 'taxrate', '会计科目'),
  createColumnWithTooltip('物料档案', 'materialarchv', 'materialarchv', '会计科目'),
];

const BANKACCOUNT_COLUMNS = [
  createColumnWithTooltip('所属二级集团编码', 'comcode', 'comcode', '银行账户'),
  createColumnWithTooltip('所属二级集团名称', 'comname', 'comname', '银行账户'),
  createColumnWithTooltip('开户单位编码', 'openunitcode', 'openunitcode', '银行账户'),
  createColumnWithTooltip('开户单位名称', 'clt_name', 'clt_name', '银行账户'),
  createColumnWithTooltip('开户单位企业级次', 'comlevel', 'comlevel', '银行账户'),
  createColumnWithTooltip('单位户名', 'account_name', 'account_name', '银行账户'),
  createColumnWithTooltip('单位账号', 'account_no', 'account_no', '银行账户'),
  createColumnWithTooltip('是否为内部单位账户', 'subcompany', 'subcompany', '银行账户'),
  createColumnWithTooltip('开户银行名称', 'ban_cnaps_name', 'ban_cnaps_name', '银行账户'),
  createColumnWithTooltip('开户机构联行号', 'bankcodenumber', 'bankcodenumber', '银行账户'),
  createColumnWithTooltip('开户银行所在国所在地区', 'country', 'country', '银行账户'),
  createColumnWithTooltip('币种', 'crncycode', 'crncycode', '银行账户'),
  createColumnWithTooltip('账户用途', 'accounttype', 'accounttype', '银行账户'),
  createColumnWithTooltip('开通银企直连标识', 'direct_type', 'direct_type', '银行账户'),
  createColumnWithTooltip('开通银企直联时间', 'direct_time', 'direct_time', '银行账户'),
  createColumnWithTooltip('集中资金账户标识', 'onlinetype', 'onlinetype', '银行账户'),
  createColumnWithTooltip('账户状态', 'accountsta', 'accountsta', '银行账户'),
  createColumnWithTooltip('开户时间', 'account_startdt', 'account_startdt', '银行账户'),
  createColumnWithTooltip('销户时间', 'account_enddt', 'account_enddt', '银行账户'),
];

const EMPLOYEE_COLUMNS = [
  createColumnWithTooltip('人员编码', 'id', 'id', '员工'),
  createColumnWithTooltip('姓名', 'full_name', 'full_name', '员工'),
  createColumnWithTooltip('性别', 'gender', 'gender', '员工'),
  createColumnWithTooltip('手机', 'mobile', 'mobile', '员工'),
  createColumnWithTooltip('所属组织', 'company_name', 'company_name', '员工'),
  createColumnWithTooltip('所属组织编码', 'belongcompany_id', 'belongcompany_id', '员工'),
  createColumnWithTooltip('所属部门', 'department_name', 'department_name', '员工'),
  createColumnWithTooltip('所属部门编码', 'parentdept_id', 'parentdept_id', '员工'),
  createColumnWithTooltip('员工状态', 'status', 'status', '员工'),
  createColumnWithTooltip('主要职务', 'main_duties', 'main_duties', '员工'),
];

const ORGANIZATION_COLUMNS = [
  createColumnWithTooltip('企业编码', 'comcode', 'comcode', '组织架构-法人组织'),
  createColumnWithTooltip('组织名称', 'name', 'name', '组织架构-法人组织'),
  createColumnWithTooltip('组织机构码', 'code', 'code', '组织架构-法人组织'),
  createColumnWithTooltip(
    '成立日期',
    'establishment_date',
    'establishment_date',
    '组织架构-法人组织',
  ),
  createColumnWithTooltip('企业层级', 'orgtiers', 'orgtiers', '组织架构-法人组织'),
  createColumnWithTooltip('国家地区', 'country_name', 'country_name', '组织架构-法人组织'),
  createColumnWithTooltip('企业性质', 'kind', 'kind', '组织架构-法人组织'),
  createColumnWithTooltip('企业法人层级', 'level', 'level', '组织架构-法人组织'),
  createColumnWithTooltip('省份', 'province', 'province', '组织架构-法人组织'),
  createColumnWithTooltip('城市', 'citycode', 'citycode', '组织架构-法人组织'),
  createColumnWithTooltip('企业规模', 'entscale', 'entscale', '组织架构-法人组织'),
  createColumnWithTooltip('是否上市', 'islisted', 'islisted', '组织架构-法人组织'),
  createColumnWithTooltip('企业所在行业', 'industry', 'industry', '组织架构-法人组织'),
  createColumnWithTooltip('注册资金（万元）', 'regcapital', 'regcapital', '组织架构-法人组织'),
  createColumnWithTooltip('企业状态', 'companystatus', 'companystatus', '组织架构-法人组织'),
  createColumnWithTooltip('是否人力资源组织', 'ismgmtorg', 'ismgmtorg', '组织架构-法人组织'),
  createColumnWithTooltip('上级公司', 'parententname', 'parententname', '组织架构-法人组织'),
  createColumnWithTooltip(
    '上级人力资源组织统一信用代码',
    'parententcode',
    'parententcode',
    '组织架构-法人组织',
  ),
  createColumnWithTooltip('是否法人组织', 'islegalentity', 'islegalentity', '组织架构-法人组织'),
  createColumnWithTooltip(
    '上级法人组织统一信用代码',
    'parentlegalentcode',
    'parentlegalentcode',
    '组织架构-法人组织',
  ),
  createColumnWithTooltip(
    '上级法人组织名称',
    'parentlegalentname',
    'parentlegalentname',
    '组织架构-法人组织',
  ),
];

export const MajorDataDetail = ({
  isModalOpen,
  majorDataId,
  majorDataName,
  setIsModalOpen,
}: {
  isModalOpen: boolean;
  majorDataId: string;
  majorDataName: string;
  setIsModalOpen: (open: boolean) => void;
}) => {
  const searchInput = useRef<InputRef>(null);

  const [searchParams, setSearchParams] = useState<Record<string, string>>({});

  const [majorDataDetail, setMajorDataDetail] = useState<any[]>([]);

  const [loading, setLoading] = useState(false);

  const columnsMap: Record<string, any[]> = {
    '组织架构-法人组织': ORGANIZATION_COLUMNS,
    '组织架构-部门': DEPARTMENT_COLUMNS,
    员工: EMPLOYEE_COLUMNS,
    银行账户: BANKACCOUNT_COLUMNS,
    会计科目: ACCOUNTS_COLUMNS,
    供应商: SUPPLIER_COLUMNS,
    客户: CUSTOMER_COLUMNS,
  };

  const fetchMajorDataDetail = async (params: Record<string, string> = searchParams) => {
    if (majorDataId) {
      setLoading(true);
      try {
        const response = await GetMajorDataDetail(majorDataId, {
          page_num: 1,
          page_size: 200,
          ...params,
        });

        if (majorDataName === '组织架构-法人组织') {
          setMajorDataDetail(
            response.data.data.data.filter(
              (item: any) => item?.ismgmtorg === '是' || item?.islegalentity === '是',
            ),
          );
          return;
        }

        if (majorDataName === '组织架构-部门') {
          setMajorDataDetail(
            response.data.data.data.filter((item: any) => item?.depttype === '普通部门'),
          );
          return;
        }

        setMajorDataDetail(response.data.data.data ?? []);
      } catch (error) {
        console.error('Error fetching major data detail:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSearch = (selectedKeys: React.Key[], confirm: () => void, dataIndex: string) => {
    const value = selectedKeys[0] as string;
    setSearchParams((prev) => {
      const next = { ...prev, [dataIndex]: value };
      fetchMajorDataDetail(next);
      return next;
    });
    confirm();
  };

  const handleReset = (
    clearFilters: (() => void) | undefined,
    dataIndex: string,
    confirm: () => void,
  ) => {
    setSearchParams((prev) => {
      const next = { ...prev };
      delete next[dataIndex];
      fetchMajorDataDetail(next);
      return next;
    });
    if (clearFilters) {
      clearFilters();
    }
    confirm();
  };

  function getColumnSearchProps(dataIndex: string): ColumnType<any> {
    return {
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: FilterDropdownProps) => (
        <div style={{ padding: 8 }}>
          <Input
            ref={searchInput}
            placeholder={`搜索 ${dataIndex}`}
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              搜索
            </Button>
            <Button
              onClick={() => handleReset(clearFilters, dataIndex, confirm)}
              size="small"
              style={{ width: 90 }}
            >
              重置
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
      onFilterDropdownOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    };
  }

  const columns = (columnsMap[majorDataName] || []).map((col) => ({
    ...col,
    ...getColumnSearchProps(col.dataIndex),
  }));

  useEffect(() => {
    fetchMajorDataDetail({});
    setSearchParams({});
  }, [majorDataId]);

  return (
    <Modal
      title={majorDataName + '主数据详情'}
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      width={1000}
      footer={null}
    >
      <Spin spinning={loading}>
        <Table
          className="max-h-[75vh]"
          sticky
          dataSource={majorDataDetail}
          columns={columns}
          rowKey={(record, idx) => record.id ?? idx}
          pagination={false}
          scroll={{ x: 'max-content', y: '70vh' }}
        />
      </Spin>
    </Modal>
  );
};
