import { ApprovalDetailData, getApprovalDetail } from '@/services/dataResources/reportApi';
import { Col, Form, Input, Row, Spin } from 'antd';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';

export const ApprovalDetail = ({
  id,
  setCanApprovalDetail,
}: {
  id?: string;
  setCanApprovalDetail: Dispatch<SetStateAction<boolean>>;
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ApprovalDetailData>();
  const [form] = Form.useForm();
  useEffect(() => {
    if (!id) return;
    const getData = async () => {
      setLoading(true);
      try {
        const response = await getApprovalDetail({ id });
        if (response.code !== 200001) return;
        setData(response.data);
        setCanApprovalDetail(response.data?.operations?.can_update);
      } finally {
        setLoading(false);
      }
    };
    getData();
  }, [id]);
  useEffect(() => {
    form.setFieldsValue({
      ...data,
    });
  }, [data]);
  return (
    <Spin spinning={loading}>
      <Form labelCol={{ flex: '80px' }} labelWrap form={form}>
        <Row gutter={8}>
          <Col span={12}>
            <Form.Item label="审批人" name="approver">
              <Input disabled={!loading} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="职位" name="approver_position">
              <Input disabled={!loading} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="联系方式" name="approver_mobile">
              <Input disabled={!loading} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="审批日期" name="approved_at">
              <Input disabled={!loading} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="审批节点" name="current_node">
              <div>{data?.current_node || '-'}</div>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="审批结果" name="status">
              <div>{data?.status || '-'}</div>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="反馈意见" name="advice">
              <div>{data?.advice || '-'}</div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Spin>
  );
};
