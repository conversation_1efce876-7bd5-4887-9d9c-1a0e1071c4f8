import { ReactNode } from 'react';

interface DataListContentProps {
  children?: ReactNode;
  extra?: string | ReactNode;
}

export const DataListContent = ({ children, extra }: DataListContentProps) => {
  return (
    <div className="text-base space-x-2 flex">
      <div className="flex-1">{children}</div>
      {extra && <div className="flex-shrink-0 flex-grow-0">{extra}</div>}
    </div>
  );
};

DataListContent.displayName = 'DataListContentProps';
