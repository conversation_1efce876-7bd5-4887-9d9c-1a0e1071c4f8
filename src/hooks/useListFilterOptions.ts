import { type FilterOption } from '@/components/SearchBox';
import {
  getOrgs,
  type OrgsData,
  type OrgsQueryParams,
  ResponseData,
} from '@/services/dataResources/orgsApi';
import { useCallback, useEffect, useMemo, useState } from 'react';

export const LIST_FILTER_OPTIONS: FilterOption[] = [
  {
    label: '数据量',
    key: 'amount_range',
    options: [
      { label: '0~99', key: '0~99' },
      { label: '100~999', key: '100~999' },
      { label: '1000~9999', key: '1000~9999' },
      { label: '10000~99999', key: '10000~99999' },
      { label: '100000以上', key: '100000以上' },
    ],
  },
  {
    label: '一级业务域',
    key: 'first_biz_domain',
    options: [
      { label: '运营管理', key: '运营管理' },
      { label: '人力资源', key: '人力资源' },
      { label: '战略投资', key: '战略投资' },
      { label: '财务金融', key: '财务金融' },
    ],
  },
  {
    label: '更新频率',
    key: 'update_frequency',
    options: [
      { label: '每日', key: '每日' },
      { label: '每周', key: '每周' },
      { label: '每月', key: '每月' },
      { label: '每季度', key: '每季度' },
      { label: '实时', key: '实时' },
    ],
  },
  {
    label: '归口部门/单位',
    key: 'owner_dept_id',
    // multiple: true,
    options: [],
  },
  {
    label: '二级业务域',
    key: 'second_biz_domain',
    options: [
      { label: '企业运营', key: '企业运营' },
      { label: '供应链管理', key: '供应链管理' },
      { label: '安全管理', key: '安全管理' },
      { label: '质量管理', key: '质量管理' },
      { label: '环保管理', key: '环保管理' },
    ],
  },
  {
    label: '三级业务域',
    key: 'third_biz_domain',
    options: [
      { label: '公司管理', key: '公司管理' },
      { label: '采购管理', key: '采购管理' },
      { label: '合同管理', key: '合同管理' },
      { label: '客户管理', key: '客户管理' },
      { label: '供应商管理', key: '供应商管理' },
      { label: '票据管理', key: '票据管理' },
      { label: '安全生产', key: '安全生产' },
      { label: '安全培训', key: '安全培训' },
    ],
  },
  {
    label: '数据库类型',
    key: 'db_type',
    options: [
      { label: 'MySQL', key: 'MySQL' },
      { label: 'PostgreSQL', key: 'PostgreSQL' },
      { label: 'SQLite', key: 'SQLite' },
      { label: 'MongoDB', key: 'MongoDB' },
    ],
  },
  {
    label: '安全等级',
    key: 'security_level',
    options: [
      { label: '一般', key: '一般' },
      { label: '重要', key: '重要' },
      { label: '核心', key: '核心' },
    ],
  },
  {
    label: '共享范围',
    key: 'share_type',
    options: [
      { label: '集团部门内共享', key: '集团部门内共享' },
      { label: '集团共享', key: '集团共享' },
      { label: '外部共享', key: '外部共享' },
    ],
  },
];

export const useListFilterOptions = (params: OrgsQueryParams) => {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<FilterOption[]>([]);

  const getListData = useCallback(async (params: Partial<OrgsQueryParams>) => {
    setLoading(true);
    try {
      const response: ResponseData<OrgsData[]> = await getOrgs({
        ...params,
      });
      if (response.code !== 200001) return;
      const newOptions = LIST_FILTER_OPTIONS.map((item) => {
        if (item.key === 'owner_dept_id') {
          return {
            ...item,
            options: response.data.map((item) => ({
              label: item.name,
              key: item.id,
            })),
          };
        }
        return item;
      });
      setOptions(newOptions);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    getListData(params);
  }, []);

  const optionsObj = useMemo<Record<string, FilterOption>>(
    () => Object.fromEntries(options.map((item) => [item.key, item])),
    [options],
  );

  return {
    loading,
    options,
    optionsObj,
  };
};
