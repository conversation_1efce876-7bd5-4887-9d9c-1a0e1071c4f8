/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};
  const userList = ['wuping', 'admin', 'xiexiaobin'];
  return {
    canAdmin: currentUser && userList.includes(currentUser.username),
    accessToAccountManagement:
      ['sa', 'dept_admin', 'dept_head'].some((role) => currentUser?.roles?.includes(role)) &&
      !currentUser?.roles?.includes('reporter'),
    normal: currentUser && !currentUser?.roles?.includes('reporter'),
  };
}
