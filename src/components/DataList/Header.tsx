import { ReactNode } from 'react';

interface DataListHeaderProps {
  children?: ReactNode;
  extra?: string | ReactNode;
}

export const DataListHeader = ({ children, extra }: DataListHeaderProps) => {
  return (
    <div className="text-base space-x-2 flex">
      <div className="flex-1 gap-2 flex">{children}</div>
      {extra && (
        <div className="flex-shrink-0 flex-grow-0 text-sm text-[#999] space-x-2">{extra}</div>
      )}
    </div>
  );
};

DataListHeader.displayName = 'DataListHeaderProps';
