// 接口
import { request } from 'umi';

export interface ResponseData<T> {
  code: number;
  data: T;
  message: string;
}

/**
 * 列表
 */
export interface ListQueryParams {
  /**
   * 数据量
   */
  amount_range?: string;
  /**
   * 当前页
   */
  current: string | number;
  /**
   * 数据库类型
   */
  db_type?: string;
  /**
   * 一级业务域
   */
  first_biz_domain?: string;
  /**
   * 资源名称
   */
  name?: string | number;
  /**
   * 资源编号
   */
  num?: string;
  /**
   * 归口部门 id
   */
  owner_dept_id?: string;
  /**
   * 每页的数据量
   */
  pageSize: string | number;
  /**
   * 二级业务域
   */
  second_biz_domain?: string;
  /**
   * 安全等级
   */
  security_level?: string;
  /**
   * 共享类型
   */
  share_type?: string;
  /**
   * 数据表名称
   */
  table_name?: string;
  /**
   * 三级业务域
   */
  third_biz_domain?: string;
  /**
   * 0：我上报的，1:待我审批的
   */
  type: string;
  /**
   * 更新频率
   */
  update_frequency?: string;
  [property: string]: any;
}

export interface ListItem {
  /**
   * 数据量
   */
  amount: number;
  approval_status: number;
  /**
   * 审批状态
   */
  approval_status_str: string;
  /**
   * 业务规模
   */
  biz_scale: string;
  creator_id: string;
  /**
   * 一级业务域
   */
  first_biz_domain: string;
  /**
   * 负责人姓名
   */
  head_name: string;
  /**
   * 上报申请 id
   */
  id: string;
  /**
   * 主要数据信息
   */
  main_info: string;
  /**
   * 资源名称
   */
  name: string;
  /**
   * 资源编号
   */
  num: string;
  /**
   * 操作权限
   */
  operations: {
    /**
     * 发布
     */
    can_release: boolean;
    /**
     * 修订申请
     */
    can_update: boolean;
    /**
     * 审批详情
     */
    can_view_approval: boolean;
    /**
     * 资源详情
     */
    can_view_resource: boolean;
    [property: string]: any;
  };
  /**
   * 拥有部门
   */
  owner_dept: string;
  /**
   * 拥有部门ID
   */
  owner_dept_id: string;
  /**
   * 二级业务域
   */
  second_biz_domain: string;
  /**
   * 数据安全等级
   */
  security_level: string;
  /**
   * 开放共享类型
   */
  share_type: string;
  /**
   * 来源渠道类别
   */
  source_channel: string;
  /**
   * 来源系统或报表名称
   */
  source_name: string;
  status: number;
  /**
   * 状态
   */
  status_str: string;
  /**
   * 三级业务域
   */
  third_biz_domain: string;
  /**
   * 更新频率
   */
  update_frequency: string;
  [property: string]: any;
}

export interface ListData {
  /**
   * 列表
   */
  list: ListItem[];
  /**
   * 总数
   */
  total: number;
  [property: string]: any;
}

export function getList(params: ListQueryParams) {
  return request('/api/v1/resource-report', {
    params,
  });
}

/**
 * 数据上报
 */

export interface ResourceReportBody {
  /**
   * 数据量
   */
  amount: number;
  /**
   * 业务规模
   */
  biz_scale: string;
  /**
   * 所在库或服务器名称
   */
  db_name: string;
  /**
   * 数据库或文件类型
   */
  db_type: string;
  /**
   * 一级业务域
   */
  first_biz_domain: string;
  /**
   * 数据资源负责人电子邮箱
   */
  head_email: string;
  /**
   * 数据资源负责人姓名
   */
  head_name: string;
  /**
   * 数据资源负责人联系电话
   */
  head_phone: string;
  /**
   * 主要数据信息
   */
  main_info: string;
  /**
   * 资源名称
   */
  name: string;
  /**
   * 拥有部门ID
   */
  owner_dept_id: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级业务域
   */
  second_biz_domain: string;
  /**
   * 数据安全等级
   */
  security_level: string;
  /**
   * 敏感信息强调说明
   */
  sensitive_desc: string;
  /**
   * 敏感信息采集或共享策略
   */
  sensitive_strategy: string;
  /**
   * 开放共享类型
   */
  share_type: string;
  /**
   * 来源渠道类别
   */
  source_channel: string;
  /**
   * 其他数据来源
   */
  source_channel_other: string;
  /**
   * 来源系统或报表名称
   */
  source_name: string;
  /**
   * 数据资源存储方式
   */
  storage_type: string;
  /**
   * 所在表或文件路径名称
   */
  table_name: string;
  /**
   * 三级业务域
   */
  third_biz_domain: string;
  /**
   * 更新频率
   */
  update_frequency: string;
  /**
   * 数据信息可用性说明
   */
  usability_desc: string;
  [property: string]: any;
}

export function resourceReport(data: ResourceReportBody) {
  return request('/api/v1/resource-report', {
    method: 'POST',
    data,
  });
}

/**
 * 审批详情
 */
export interface ApprovalDetailParams {
  id: string;
}

export interface ApprovalDetailData {
  /**
   * 审批意见
   */
  advice: string;
  /**
   * 审批ID
   */
  approval_id: string;
  /**
   * 审批时间
   */
  approved_at: string;
  /**
   * 审批人
   */
  approver: string;
  /**
   * 审批人手机号
   */
  approver_mobile: string;
  /**
   * 审批人职位
   */
  approver_position: string;
  /**
   * 当前审批节点
   */
  current_node: string;
  /**
   * 状态
   */
  status: string;
  [property: string]: any;
}

export function getApprovalDetail(params: ApprovalDetailParams) {
  return request(`/api/v1/resource-report/${params.id}/approval-detail`);
}

/**
 * 发布
 */
export interface ReleaseBody {
  id: string;
}

export function release(data: ReleaseBody) {
  return request(`/api/v1/resource-report/${data.id}/release`, {
    method: 'POST',
  });
}

/**
 * 处理审批页面
 */

export interface ApprovalPageData {
  /**
   * 审批意见
   */
  advice: string;
  amount: number;
  /**
   * 申请人单位名称
   */
  applicant_company: string;
  /**
   * 申请日期
   */
  applicant_date: string;
  /**
   * 申请人部门名称
   */
  applicant_dept: string;
  /**
   * 申请人联系方式
   */
  applicant_mobile: string;
  /**
   * 申请人姓名
   */
  applicant_name: string;
  /**
   * 申请人职位
   */
  applicant_position: string;
  /**
   * 审批ID
   */
  approval_id: string;
  /**
   * 审批时间
   */
  approved_at: string;
  /**
   * 审批人
   */
  approver: string;
  /**
   * 审批人手机号
   */
  approver_mobile: string;
  /**
   * 审批人职位
   */
  approver_position: string;
  biz_scale: string;
  /**
   * 当前审批节点ID
   */
  current_log_id: string;
  /**
   * 当前审批节点
   */
  current_node: string;
  db_name: string;
  db_type: string;
  first_biz_domain: string;
  head_email: string;
  head_name: string;
  head_phone: string;
  id: string;
  main_info: string;
  name: string;
  /**
   * 编号
   */
  num: string;
  /**
   * 拥有部门
   */
  owner_dept: string;
  owner_dept_id: string;
  /**
   * 是否通过
   */
  pass: boolean;
  remark: string;
  second_biz_domain: string;
  security_level: string;
  sensitive_desc: string;
  sensitive_strategy: string;
  share_type: string;
  source_channel: string;
  source_channel_other: string;
  source_name: string;
  storage_type: string;
  table_name: string;
  third_biz_domain: string;
  update_frequency: string;
  usability_desc: string;
  [property: string]: any;
}

export interface ApprovalPageParams {
  id: string;
}

export function getApprovalPage(params: ApprovalPageParams) {
  return request(`/api/v1/resource-report/${params.id}/approval-page`);
}
// 提交审批
export interface ApprovalBody {
  /**
   * 审批意见
   */
  advice: string;
  /**
   * 是否通过
   */
  pass: boolean;
  [property: string]: any;
}

export function approval(id: string, data: ApprovalBody) {
  return request(`/api/v1/resource-report/${id}/approval`, {
    method: 'POST',
    data,
  });
}

/**
 * 资源详情
 */
export interface ResourceData {
  /**
   * 数据量
   */
  amount: number;
  /**
   * 业务规模
   */
  biz_scale: string;
  /**
   * 所在库或服务器名称
   */
  db_name: string;
  /**
   * 数据库或文件类型
   */
  db_type: string;
  /**
   * 一级业务域
   */
  first_biz_domain: string;
  /**
   * 数据资源负责人电子邮箱
   */
  head_email: string;
  /**
   * 数据资源负责人姓名
   */
  head_name: string;
  /**
   * 数据资源负责人联系电话
   */
  head_phone: string;
  /**
   * 资源 id
   */
  id: string;
  /**
   * 主要数据信息
   */
  main_info: string;
  /**
   * 资源名称
   */
  name: string;
  /**
   * 资源编号
   */
  num: string;
  /**
   * 操作权限
   */
  operations: {
    /**
     * 发布
     */
    can_release: boolean;
    /**
     * 修订申请
     */
    can_update: boolean;
    /**
     * 审批详情
     */
    can_view_approval: boolean;
    /**
     * 资源详情
     */
    can_view_resource: boolean;
    [property: string]: any;
  };
  /**
   * 拥有部门
   */
  owner_dept: string;
  /**
   * 拥有部门ID
   */
  owner_dept_id: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级业务域
   */
  second_biz_domain: string;
  /**
   * 数据安全等级
   */
  security_level: string;
  /**
   * 敏感信息强调说明
   */
  sensitive_desc: string;
  /**
   * 敏感信息采集或共享策略
   */
  sensitive_strategy: string;
  /**
   * 开放共享类型
   */
  share_type: string;
  /**
   * 来源渠道类别
   */
  source_channel: string;
  /**
   * 其他数据来源
   */
  source_channel_other?: string;
  /**
   * 来源系统或报表名称
   */
  source_name: string;
  /**
   * 数据资源存储方式
   */
  storage_type: string;
  /**
   * 所在表或文件路径名称
   */
  table_name: string;
  /**
   * 三级业务域
   */
  third_biz_domain: string;
  /**
   * 更新频率
   */
  update_frequency: string;
  /**
   * 数据信息可用性说明
   */
  usability_desc: string;
  [property: string]: any;
}

export function getResource(id: string) {
  return request(`/api/v1/resource-report/${id}`);
}

/**
 * 编辑更改
 */
export interface ReportEditBody {
  amount: number;
  biz_scale: string;
  db_name: string;
  db_type: string;
  first_biz_domain: string;
  head_email: string;
  head_name: string;
  head_phone: string;
  main_info: string;
  name: string;
  owner_dept_id: string;
  remark: string;
  second_biz_domain: string;
  security_level: string;
  sensitive_desc: string;
  sensitive_strategy: string;
  share_type: string;
  source_channel: string;
  source_channel_other: string;
  source_name: string;
  storage_type: string;
  table_name: string;
  third_biz_domain: string;
  update_frequency: string;
  usability_desc: string;
  [property: string]: any;
}

export function reportEdit(id: string, data: ReportEditBody) {
  return request(`/api/v1/resource-report/${id}/edit`, {
    method: 'PUT',
    data,
  });
}
