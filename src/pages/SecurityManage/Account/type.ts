export interface AccountItem {
  avatar: string;
  company_id: string;
  created_at: string;
  deleted_at: null | string;
  dept_id: null | string;
  email: string;
  full_name: string;
  id: string;
  mobile: string;
  position: string;
  register_status: string;
  roles: string[];
  status: boolean;
  updated_at: string;
  username: string;

  _password?: string;
}

export interface AccountResponse {
  code: number;
  data: {
    list: AccountItem[];
    total: number;
  };
  message: string;
}
