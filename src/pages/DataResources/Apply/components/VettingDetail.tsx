import {
  getApplyMasterDataApprovalDetail,
  getResourceUseListItemVettingDetail,
  ResourceUseListItemVettingDetail,
  ResourceUseResponse,
} from '@/services/dataResources/applyApi';
import { Col, Form, Input, message, Row, Spin } from 'antd';
import { useEffect, useState } from 'react';

export const VettingDetail = ({ id, isMasterData }: { id?: string; isMasterData?: boolean }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (!id) return;
    if (isMasterData) {
      (async () => {
        setLoading(true);
        try {
          const { code, message, data } = await getApplyMasterDataApprovalDetail(id);
          if (code !== 200001) {
            message.error(message);
            return;
          }
          form.setFieldsValue({ ...data });
        } catch (error) {
          message.error(JSON.stringify(error));
        } finally {
          setLoading(false);
        }
      })();
    } else {
      (async () => {
        setLoading(true);
        try {
          const response: ResourceUseResponse<ResourceUseListItemVettingDetail> =
            await getResourceUseListItemVettingDetail(id);

          if (response.code !== 200001) {
            message.error(response.message);
            return;
          }

          form.setFieldsValue({ ...response.data });
        } catch (error) {
          message.error(JSON.stringify(error));
        } finally {
          setLoading(false);
        }
      })();
    }
  }, [id]);

  return (
    <Spin spinning={loading}>
      <Form labelCol={{ flex: '80px' }} labelWrap form={form}>
        <Row gutter={8}>
          <Col span={12}>
            <Form.Item label="审批人" name="approver">
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="职位" name="approver_position">
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="联系方式" name="approver_mobile">
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="审批日期" name="approved_at">
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="审批节点" name="current_node">
              <div>{form.getFieldValue('current_node') || '-'}</div>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="审批结果" name="status">
              <div>{form.getFieldValue('status') || '-'}</div>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="反馈意见" name="advice">
              <div>{form.getFieldValue('advice') || '-'}</div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Spin>
  );
};
