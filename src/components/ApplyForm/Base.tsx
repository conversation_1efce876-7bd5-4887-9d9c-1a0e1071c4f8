import { Col, Form, Input, Row, Select, Tooltip, TreeSelect } from 'antd';
import { Rule } from 'antd/es/form';
import { type DefaultOptionType } from 'antd/es/select';
import { useContext } from 'react';
import { Context } from '.';
import { InfoCircleOutlined } from '@ant-design/icons';

export interface CascaderOption {
  value: string;
  label: string;
  children?: CascaderOption[];
}

interface ApplyFormBaseProps {
  rules?: Record<string, Rule[]>;
  showOwnerDeptDesc?: boolean;
  ownerDeptOptions?: CascaderOption[];
  sourceChannelOptions?: DefaultOptionType[];
  disabled?: boolean;
}

// 基础信息
export const ApplyFormBase = ({
  ownerDeptOptions,
  sourceChannelOptions,
  showOwnerDeptDesc,
  rules,
  disabled,
}: ApplyFormBaseProps) => {
  const { itemSpan, itemGutter } = useContext(Context);
  return (
    <Row gutter={itemGutter}>
      <Col span={itemSpan}>
        <Form.Item label="数据资源序列编号" name="num" rules={rules?.num} required>
          <Input placeholder="系统自动生成" disabled={true} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          name="name"
          label={
            <div className="flex items-center gap-2">
              数据资源中文名称
              <Tooltip title="该资源对应的中文业务名称或者报表的完整名称">
                <InfoCircleOutlined />
              </Tooltip>
            </div>
          }
          rules={rules?.name}
        >
          <Input placeholder="请输入中文名称" disabled={disabled} />
        </Form.Item>
      </Col>
      <Col span={itemSpan}>
        <Form.Item
          name="owner_dept_id"
          label={
            <div className="flex items-center gap-2">
              拥有公司及部门
              <Tooltip title="如果资源来源于集团公司外部，应给出数据来源的渠道公司或机构名称">
                <InfoCircleOutlined />
              </Tooltip>
            </div>
          }
          rules={rules?.owner_dept_id}
        >
          <TreeSelect
            placeholder="请选择拥有者及部门"
            popupMatchSelectWidth={false}
            treeData={ownerDeptOptions}
            disabled={disabled}
          />
        </Form.Item>
      </Col>
      {showOwnerDeptDesc && (
        <Col span={itemSpan}>
          <Form.Item
            name="owner_dept_detail"
            label="公司或部门具体来源"
            rules={rules?.owner_dept_detail}
          >
            <Input placeholder="请填写公司或部门具体来源" disabled={disabled} />
          </Form.Item>
        </Col>
      )}
      <Col span={itemSpan}>
        <Form.Item name="source_channel" label="来源渠道类别" rules={rules?.source_channel}>
          <Select
            placeholder="请选择来源渠道类别"
            options={sourceChannelOptions}
            disabled={disabled}
          />
        </Form.Item>
      </Col>
      <Col span={itemSpan}>
        <Form.Item
          name="source_name"
          label={
            <div className="flex items-center gap-2">
              来源系统或报表名称
              <Tooltip title="线上信息系统：中文名称及其英文简称；线下手工报表：完整准确的报表名称和文件格式的描述；其他数据来源：相关数据来源机构和来源方式等信息的描述">
                <InfoCircleOutlined />
              </Tooltip>
            </div>
          }
          rules={rules?.source_name}
        >
          <Input placeholder="填写来源系统或报表名称" disabled={disabled} />
        </Form.Item>
      </Col>
    </Row>
  );
};

ApplyFormBase.displayName = 'ApplyFormBase';
