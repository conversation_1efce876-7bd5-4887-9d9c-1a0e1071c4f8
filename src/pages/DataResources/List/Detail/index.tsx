import { ReactComponent as CompanyIcon } from '@/assets/center_icon.svg';
import { ReactComponent as TitleIcon } from '@/assets/list_detail_title.svg';
import { ReactComponent as SafeIcon } from '@/assets/safe_icon.svg';
import { ReactComponent as SensitiveIcon } from '@/assets/sensitive_icon.svg';
import { ReactComponent as ShareIcon } from '@/assets/share_icon.svg';
import { GetListDetail } from '@/services/dataResources/listApi';
import {
  Button,
  Card,
  Collapse,
  CollapseProps,
  ConfigProvider,
  Descriptions,
  message,
  Segmented,
  Space,
  Spin,
} from 'antd';
import Meta from 'antd/es/card/Meta';
import React, { useEffect } from 'react';
import { history, useParams } from 'umi';
import { Application } from '../Components/aplication';
import DetailDataList from '../Components/dataList';

//titleCard
const TitleCard = ({ data, id }: any) => {
  const IconText = ({ icon, text }: { icon: any; text: string }) => (
    <Space size="small">
      {icon}
      <span className="text-[#333]">{text}</span>
    </Space>
  );

  return (
    <Card>
      <Meta
        avatar={<TitleIcon />}
        title={
          <div className="flex items-center justify-between">
            <div>{data?.name}</div>
            <div className="flex gap-2 text-[14px] font-normal">
              <div className={`p-1 flex items-center gap-2 bg-[#002fa50d] text-[#002fa5] h-[26px]`}>
                {data?.first_biz_domain}
              </div>
              <div
                className={`p-1 flex items-center gap-2 bg-[#BAA40017] text-[#BAA400]  h-[26px]`}
              >
                {data?.owner_dept}
              </div>
              <div
                className={`p-1 flex items-center gap-2 bg-[#00BAA412] text-[#00BAA4]  h-[26px]`}
              >
                {data?.name}（{data?.source_name}）
              </div>
            </div>
          </div>
        }
        description={
          <div className="font-normal text-[#333] flex flex-col gap-3">
            <div className="text-[14px] leading-[19.6px] text-[#999]">
              数据资源序列编号：{data?.num}
            </div>
            <div className="text-[14px] leading-[19.6px] flex gap-3">
              <IconText icon={<CompanyIcon />} text={data?.owner_dept} key="company" />
              <IconText icon={<ShareIcon />} text={data?.share_type} key="list-vertical-like-o" />
              <IconText
                icon={<SafeIcon />}
                text={`安全等级: ${data?.security_level || ''}`}
                key="list-vertical-like-o"
              />
              <IconText
                icon={<SensitiveIcon />}
                text={data?.sensitive_strategy}
                key="list-vertical-message"
              />
            </div>
            <div className="flex gap-3">
              <Button size="small">订阅通知</Button>
              <Button size="small">下载到本地</Button>
              {/* 使用申请 */}
              {data?.operations?.can_apply_use && <Application type="detail" id={id} />}
              {data?.operations?.can_update && (
                <Button
                  type="primary"
                  size="small"
                  onClick={() => history.push(`/dataResources/report/apply?id=${id}`)}
                >
                  编辑修改
                </Button>
              )}
            </div>
          </div>
        }
      />
    </Card>
  );
};

//content
const ContentCollapse = ({ data }: any) => {
  const onChange = (key: string | string[]) => {
    console.log(key);
  };
  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: '基础信息',
      children: (
        <Descriptions
          column={2}
          items={[
            {
              key: '1',
              label: '中文名称',
              children: data.name,
            },
            {
              key: '2',
              label: '资源序列编号',
              children: data.num,
            },
            {
              key: '3',
              label: '拥有者',
              children: data.owner_dept,
            },
            {
              key: '4',
              label: '来源渠道',
              children: data.source_channel,
            },
            {
              key: '5',
              label: '来源系统/报表',
              children: `${data?.name || ''}(${data?.source_name || ''})`,
            },
          ]}
        />
      ),
    },
    {
      key: '2',
      label: '业务信息',
      children: (
        <Descriptions
          column={2}
          items={[
            {
              key: '1',
              label: '一级业务域',
              children: data.first_biz_domain,
            },
            {
              key: '2',
              label: '二级业务域',
              children: data.second_biz_domain,
            },
            {
              key: '3',
              label: '三级业务域',
              children: data.third_biz_domain,
            },
          ]}
        />
      ),
    },
    {
      key: '3',
      label: '技术信息',
      children: (
        <Descriptions
          column={2}
          items={[
            {
              key: '1',
              label: '主要数据信息',
              children: data?.data_fields
                ? `${data?.main_info || ''}(${data?.data_fields})`
                : data?.main_info,
              span: 2,
            },
            {
              key: '2',
              label: '数据安全等级',
              children: data?.security_level,
            },
            {
              key: '3',
              label: '开放共享类型',
              children: data?.share_type,
            },
            {
              key: '4',
              label: '敏感信息强调说明',
              children: data?.sensitive_desc,
            },
            {
              key: '5',
              label: '敏感信息采集/共享策略',
              children: data?.sensitive_strategy,
            },
            {
              key: '6',
              label: '更新频率',
              children: data?.update_frequency,
            },
            {
              key: '7',
              label: '业务规模',
              children: data?.biz_scale,
            },
            {
              key: '8',
              label: '可用性说明',
              children: data?.usability_desc,
            },
            {
              key: '9',
              label: '数据库/文件类型',
              children: data?.db_type,
            },
            {
              key: '10',
              label: '所在库/服务器名称',
              children: data?.db_name,
            },
            {
              key: '11',
              label: '所在表/路径名称',
              children: data?.table_name,
            },
            {
              key: '12',
              label: '存储形式',
              children: data?.storage_type,
            },
          ]}
        />
      ),
    },
    {
      key: '4',
      label: '管理信息',
      children: (
        <Descriptions
          column={2}
          items={[
            {
              key: '1',
              label: '负责人姓名',
              children: data?.head_name,
            },
            {
              key: '2',
              label: '联系电话',
              children: data?.head_phone,
            },
            {
              key: '3',
              label: '电子邮箱',
              children: data?.head_email,
            },
            {
              key: '4',
              label: '备注',
              children: data?.remark,
            },
          ]}
        />
      ),
    },
  ];
  return (
    <ConfigProvider
      theme={{
        components: {
          Collapse: {
            contentBg: '#ffffff',
            headerBg: '#ffffff',
            contentPadding: '20px 20px 18px 24px',
            headerPadding: '16px 20px 16px 24px',
          },
        },
      }}
    >
      <Collapse
        bordered={false}
        items={items}
        defaultActiveKey={['1', '2', '3', '4']}
        onChange={onChange}
      />
    </ConfigProvider>
  );
};

function ListDetail() {
  const { id } = useParams();
  //detail数据
  const [detailData, setDetailData] = React.useState<any>([]);
  //加载
  const [loading, setLoading] = React.useState<any>(false);
  //资源 tab
  const [alignValue, setAlignValue] = React.useState<any>('资源信息');

  //获取详情数据
  const getDeatil = async () => {
    setLoading(true);
    const res = await GetListDetail(id);
    if (res.code === 200001) {
      setDetailData(res.data);
      setLoading(false);
    } else {
      message.error(res.message);
      setLoading(false);
    }
  };

  useEffect(() => {
    getDeatil();
  }, [id]);

  return (
    <Spin spinning={loading}>
      <ConfigProvider
        theme={{
          components: {
            Segmented: {
              itemSelectedBg: '#fff',
              itemSelectedColor: '#002FA5',
              itemHoverColor: '#002FA5',
              itemHoverBg: 'rgba(0, 47, 165, 0.15)',
            },
            Table: {
              headerBg: '#F3F5FB',
            },
          },
        }}
      >
        <div className="list_detail_collapse">
          <div>
            <TitleCard data={detailData} id={id} />
          </div>
          <div>
            <Segmented
              value={alignValue}
              size="large"
              style={{ margin: '10px 0' }}
              onChange={setAlignValue}
              options={['资源信息', '数据项明细']}
            />
            {alignValue === '资源信息' && <ContentCollapse data={detailData} />}
            {alignValue === '数据项明细' && <DetailDataList data={detailData} />}
          </div>
        </div>
      </ConfigProvider>
    </Spin>
  );
}
export default ListDetail;
